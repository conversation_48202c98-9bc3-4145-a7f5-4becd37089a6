import 'dart:async';

import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:emartdriver/constants.dart';
import 'package:emartdriver/firebase_options.dart';
import 'package:emartdriver/model/User.dart';
import 'package:emartdriver/services/FirebaseHelper.dart';
import 'package:emartdriver/services/ServicoMonitoramentoBloqueio.dart';
import 'package:emartdriver/services/fcm_token_service.dart';
import 'package:emartdriver/services/notification_handler_service.dart';
import 'package:emartdriver/services/notification_service.dart';
import 'package:emartdriver/services/status_entregador_service.dart';
import 'package:emartdriver/services/verificacao_atualizacao_service.dart';
import 'package:emartdriver/ui/auth/AuthScreen.dart';
import 'package:emartdriver/ui/container/ContainerScreen.dart';
import 'package:emartdriver/ui/documents/DocumentsResubmitScreen.dart';
import 'package:emartdriver/userPrefrence.dart';
import 'package:firebase_app_check/firebase_app_check.dart';
import 'package:firebase_auth/firebase_auth.dart' as auth;
import 'package:firebase_core/firebase_core.dart';
import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:shared_preferences/shared_preferences.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();
  await EasyLocalization.ensureInitialized();
  await UserPreference.init();

  await _inicializarFirebaseENotificacoes();
  // Inicializar porta de comunicação do flutter_foreground_task

  StatusEntregadorService().initialize();

  SharedPreferences sp = await SharedPreferences.getInstance();

  runApp(
    EasyLocalization(
      supportedLocales: const [Locale('pt', 'BR')],
      path: 'assets/translations',
      fallbackLocale: Locale(sp.getString('languageCode') ?? 'pt', 'BR'),
      saveLocale: true,
      useOnlyLangCode: true,
      useFallbackTranslations: true,
      child: const MyApp(),
    ),
  );
}

BuildContext? globalContext;

Future<void> _firebaseMessagingBackgroundHandler(RemoteMessage message) async {
  await Firebase.initializeApp(
    options: DefaultFirebaseOptions.currentPlatform,
  );
}

Future<void> _inicializarFirebaseENotificacoes() async {
  try {
    await Firebase.initializeApp(
        options: DefaultFirebaseOptions.currentPlatform);

    auth.FirebaseAuth.instance.setLanguageCode('pt');
    await FirebaseAppCheck.instance.activate(
      webProvider:
          ReCaptchaV3Provider('6LfKkmUrAAAAADfZuZRrmQLmRNzVv8cauwycCu0K'),
      androidProvider: AndroidProvider.playIntegrity,
      appleProvider: AppleProvider.appAttest,
    );

    // Inicializar FCM Token Service (substitui configurações manuais)
    await FcmTokenService.initialize();

    FirebaseMessaging.onBackgroundMessage(_firebaseMessagingBackgroundHandler);
  } catch (e) {
    // Erro na inicialização do Firebase/FCM tratado silenciosamente
  }
}

class MyApp extends StatefulWidget {
  const MyApp({super.key});

  @override
  MyAppState createState() => MyAppState();
}

class MyAppState extends State<MyApp> with WidgetsBindingObserver {
  static User? currentUser;

  final GlobalKey<NavigatorState> navigatorKey =
      GlobalKey(debugLabel: 'Main Navigator');

  NotificationService notificationService = NotificationService();
  NotificationHandlerService notificationHandler = NotificationHandlerService();

  @override
  Widget build(BuildContext context) {
    globalContext = context;

    return MaterialApp(
        navigatorKey: notificationService.navigatorKey,
        localizationsDelegates: context.localizationDelegates,
        supportedLocales: context.supportedLocales,
        locale: context.locale,
        title: 'Tá entregue Driver'.tr(),
        builder: EasyLoading.init(),
        theme: ThemeData(
            appBarTheme: AppBarTheme(
                centerTitle: true,
                color: Colors.transparent,
                elevation: 0,
                actionsIconTheme: IconThemeData(color: Color(COLOR_PRIMARY)),
                iconTheme: IconThemeData(color: Color(COLOR_PRIMARY)),
                systemOverlayStyle: SystemUiOverlayStyle.dark,
                toolbarTextStyle: const TextTheme(
                        titleLarge: TextStyle(
                            color: Colors.black,
                            fontSize: 17.0,
                            letterSpacing: 0,
                            fontWeight: FontWeight.w700))
                    .bodyMedium,
                titleTextStyle: const TextTheme(
                        titleLarge: TextStyle(
                            color: Colors.black,
                            fontSize: 17.0,
                            letterSpacing: 0,
                            fontWeight: FontWeight.w700))
                    .titleLarge),
            bottomSheetTheme:
                const BottomSheetThemeData(backgroundColor: Colors.white),
            primaryColor: Color(COLOR_PRIMARY),
            brightness: Brightness.light),
        debugShowCheckedModeBanner: false,
        color: Color(COLOR_PRIMARY),
        home: const OnBoarding());
  }

  @override
  void dispose() {
    WidgetsBinding.instance.removeObserver(this);
    super.dispose();
  }

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addObserver(this);

    WidgetsBinding.instance.addPostFrameCallback((_) {
      _inicializarServicosAssincrono();
    });
  }

  notificationInit() async {
    try {
      await notificationService.initInfo();

      // Inicializar o handler de notificações globalmente
      notificationHandler.initialize();

      // Configurar listener para mudanças no token FCM
      _configurarListenerTokenFCM();
    } catch (_) {}
  }

  /// Configura listener para mudanças no token FCM
  void _configurarListenerTokenFCM() {
    FcmTokenService.onTokenRefresh().listen((newToken) async {
      if (MyAppState.currentUser != null) {
        MyAppState.currentUser!.fcmToken = newToken;
        await FireStoreUtils.updateCurrentUser(MyAppState.currentUser!);
      }
    });
  }

  /// Atualiza o token FCM do usuário atual
  Future<void> atualizarTokenUsuarioAtual() async {
    if (MyAppState.currentUser != null) {
      try {
        String? token = await FcmTokenService.getToken();

        if (token != null && FcmTokenService.isValidToken(token)) {
          // Buscar dados atualizados do usuário
          User? user = await FireStoreUtils.getCurrentUser(
              MyAppState.currentUser!.userID);
          if (user != null) {
            user.fcmToken = token;
            await FireStoreUtils.updateCurrentUser(user);
            MyAppState.currentUser = user;
          }
        }
      } catch (_) {
        // Error handled silently
      }
    }
  }

  void _inicializarServicosAssincrono() async {
    await notificationInit();
    await atualizarTokenUsuarioAtual();
  }
}

class OnBoarding extends StatefulWidget {
  const OnBoarding({super.key});

  @override
  State createState() {
    return OnBoardingState();
  }
}

class OnBoardingState extends State<OnBoarding> {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      body: Center(
        child: CircularProgressIndicator.adaptive(
          valueColor: AlwaysStoppedAnimation(
            Color(COLOR_PRIMARY),
          ),
        ),
      ),
    );
  }

  Future<Widget> determinarTelaInicial() async {
    auth.User? firebaseUser = auth.FirebaseAuth.instance.currentUser;
    if (firebaseUser == null) {
      return const AuthScreen();
    }

    User? user = await FireStoreUtils.getCurrentUser(firebaseUser.uid);
    if (user == null) {
      return const AuthScreen();
    }

    if (user.cpfCnpj != null && user.cpfCnpj != '') {
      final value = user.cpfCnpj ?? '';
      if (value.length == 11) {
        user.cpf = value;
      } else {
        user.cnpj = value;
      }
      await FireStoreUtils.updateCurrentUser(user);
    }

    if (user.role != USER_ROLE_DRIVER) {
      return const AuthScreen();
    }

    if (user.released_for_use == false) {
      return DocumentsResubmitScreen(user: user);
    }

    if (user.active) {
      user.isActive = true;
      user.role = USER_ROLE_DRIVER;

      // Obter token FCM usando o novo service
      String? token = await FcmTokenService.getToken();
      user.fcmToken = token ?? '';

      await FireStoreUtils.updateCurrentUser(user);
      MyAppState.currentUser = user;

      // Inicializar monitoramento de bloqueio
      _inicializarMonitoramentoBloqueio();

      return ContainerScreen(user: user);
    } else {
      user.isActive = false;
      user.lastOnlineTimestamp = Timestamp.now();
      await FireStoreUtils.updateCurrentUser(user);
      await auth.FirebaseAuth.instance.signOut();
      MyAppState.currentUser = null;
      return const AuthScreen();
    }
  }

  @override
  void initState() {
    super.initState();

    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (mounted) {
        _navegarParaTelaInicial();
        // Verificar atualizações de forma independente
        _verificarAtualizacaoOnBoarding();
      }
    });
  }

  /// Verifica atualizações durante o OnBoarding de forma independente
  void _verificarAtualizacaoOnBoarding() {
    // Aumentar o tempo de espera para garantir que a interface esteja completamente carregada
    Future.delayed(const Duration(seconds: 6), () async {
      try {
        await VerificacaoAtualizacaoService.verificarAtualizacaoENotificar();
      } catch (e) {
        // Erro tratado silenciosamente
      }
    });
  }

  void _inicializarMonitoramentoBloqueio() {
    final servicoMonitoramento = ServicoMonitoramentoBloqueio();

    servicoMonitoramento.iniciarMonitoramento(
      aoBloquear: (bloqueio) {
        if (mounted && context.mounted) {
          WidgetsBinding.instance.addPostFrameCallback((_) {
            if (mounted && context.mounted) {
              servicoMonitoramento.navegarParaTelaBloqueio(context, bloqueio);
            }
          });
        }
      },
      aoDesbloquear: (bloqueio) {
        // Entregador desbloqueado
      },
    );
  }

  void _navegarParaTelaInicial() async {
    try {
      if (!mounted) return;

      Widget telaDestino = await determinarTelaInicial();

      if (!mounted) return;

      // Navegar para a tela de destino
      Navigator.of(context).pushAndRemoveUntil(
        MaterialPageRoute(builder: (context) => telaDestino),
        (route) => false,
      );
    } catch (e) {
      // Erro tratado silenciosamente
    }
  }

  /// Método alternativo para verificar atualizações de forma mais direta
  /// Pode ser chamado em qualquer momento durante o OnBoarding
  static Future<void> verificarAtualizacaoSimples() async {
    try {
      await VerificacaoAtualizacaoService.verificarAtualizacaoENotificar();
    } catch (e) {
      // Erro tratado silenciosamente
    }
  }
}
