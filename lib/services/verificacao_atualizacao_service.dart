import 'dart:io';

import 'package:flutter/material.dart';
import 'package:upgrader/upgrader.dart';
import 'package:url_launcher/url_launcher.dart';

import '../main.dart';

/// Service responsável por verificar e gerenciar atualizações do aplicativo
/// nas lojas Android (Google Play) e iOS (App Store)
class VerificacaoAtualizacaoService {
  static final VerificacaoAtualizacaoService _instancia =
      VerificacaoAtualizacaoService._interno();

  factory VerificacaoAtualizacaoService() {
    return _instancia;
  }

  VerificacaoAtualizacaoService._interno();

  /// Verifica se existe atualização disponível na loja
  /// Retorna true se houver atualização disponível
  Future<bool> verificarAtualizacaoDisponivel() async {
    try {
      final upgrader = Upgrader();
      await upgrader.initialize();

      final isUpdateAvailable = upgrader.isUpdateAvailable();
      return isUpdateAvailable;
    } catch (e) {
      return false;
    }
  }

  /// Obtém informações detalhadas sobre a atualização disponível
  Future<Map<String, dynamic>?> obterInformacoesAtualizacao() async {
    try {
      final upgrader = Upgrader();
      await upgrader.initialize();

      final isUpdateAvailable = upgrader.isUpdateAvailable();

      if (!isUpdateAvailable) {
        return null;
      }

      return {
        'versaoAtual': upgrader.currentInstalledVersion ?? 'Desconhecida',
        'versaoDisponivel': upgrader.currentAppStoreVersion ?? 'Desconhecida',
        'urlLoja': _obterUrlLoja(),
        'isObrigatoria': _isAtualizacaoObrigatoria(upgrader),
        'descricao': 'Nova versão disponível com melhorias e correções.',
      };
    } catch (e) {
      return null;
    }
  }

  /// Verifica se a atualização é obrigatória baseada na diferença de versões
  bool _isAtualizacaoObrigatoria(Upgrader upgrader) {
    try {
      final currentVersion = upgrader.currentInstalledVersion!;
      final storeVersion = upgrader.currentAppStoreVersion!;

      // Considera obrigatória se a diferença for maior que uma versão minor
      final currentParts = currentVersion.split('.');
      final storeParts = storeVersion.split('.');

      if (currentParts.length >= 2 && storeParts.length >= 2) {
        final currentMajor = int.tryParse(currentParts[0]) ?? 0;
        final currentMinor = int.tryParse(currentParts[1]) ?? 0;
        final storeMajor = int.tryParse(storeParts[0]) ?? 0;
        final storeMinor = int.tryParse(storeParts[1]) ?? 0;

        // Obrigatória se versão major diferente ou minor com diferença > 1
        return (storeMajor > currentMajor) ||
            (storeMajor == currentMajor && storeMinor > currentMinor + 1);
      }

      return false;
    } catch (e) {
      return false;
    }
  }

  /// Obtém a URL da loja apropriada para o platform
  String _obterUrlLoja() {
    if (Platform.isAndroid) {
      // https: //play.google.com/store/apps/details?id=com.starnorte.taentregue
      return 'https://play.google.com/store/apps/details?id=com.starnorte.taentregue';
    } else if (Platform.isIOS) {
      // https://apps.apple.com/us/app/t%C3%A1-liso-entregador/id6744942407
      return 'https://apps.apple.com/us/app/t%C3%A1-liso-entregador/id6744942407'; // Substitua pelo ID real do app
    }
    return '';
  }

  /// Exibe o dialog de atualização com as informações fornecidas
  Future<void> exibirDialogAtualizacao({
    required Map<String, dynamic> informacoes,
    VoidCallback? onAtualizar,
    VoidCallback? onCancelar,
  }) async {
    final isObrigatoria = informacoes['isObrigatoria'] ?? false;

    // Verificar se o contexto está disponível antes de exibir o diálogo
    try {
      // Obter contexto válido
      final BuildContext context =
          MyAppState().navigatorKey.currentContext ?? globalContext!;

      // Tentar exibir o diálogo usando o contexto do aplicativo
      await showDialog(
        barrierDismissible: !isObrigatoria,
        context: context,
        builder: (dialogContext) => TelaAtualizacaoDialog(
          versaoAtual: informacoes['versaoAtual'] ?? 'Desconhecida',
          versaoDisponivel: informacoes['versaoDisponivel'] ?? 'Desconhecida',
          descricao: informacoes['descricao'] ?? 'Nova versão disponível.',
          urlLoja: informacoes['urlLoja'] ?? '',
          isObrigatoria: isObrigatoria,
          onAtualizar: onAtualizar,
          onCancelar: onCancelar,
        ),
      );
    } catch (e) {
      // Se falhar, apenas executar a ação de atualizar diretamente
      _abrirUrlDiretamente(
          informacoes['urlLoja'] as String? ?? '', onAtualizar);
    }
  }

  /// Abre a URL da loja diretamente, sem mostrar diálogo
  Future<void> _abrirUrlDiretamente(String url, VoidCallback? callback) async {
    if (url.isEmpty) return;

    try {
      // Aguardar para garantir que qualquer operação pendente seja concluída
      await Future.delayed(const Duration(milliseconds: 500));
      await launchUrl(Uri.parse(url), mode: LaunchMode.externalApplication);
      callback?.call();
    } catch (e) {
      // Erro tratado silenciosamente
    }
  }

  /// Verifica automaticamente e exibe dialog se necessário
  Future<void> verificarEExibirAtualizacao({
    VoidCallback? onAtualizar,
    VoidCallback? onCancelar,
  }) async {
    try {
      final informacoes = await obterInformacoesAtualizacao();

      if (informacoes != null) {
        exibirDialogAtualizacao(
          informacoes: informacoes,
          onAtualizar: () {
            final url = informacoes['urlLoja'] as String;
            if (url.isNotEmpty) {
              launchUrl(Uri.parse(url), mode: LaunchMode.externalApplication);
            }
            onAtualizar?.call();
          },
          onCancelar: () {
            onCancelar?.call();
          },
        );
      }
    } catch (e) {
      // Erro tratado silenciosamente
    }
  }

  /// Método para verificar atualizações de forma simplificada
  /// Pode ser usado de qualquer lugar
  static Future<void> verificarAtualizacaoENotificar() async {
    try {
      final service = VerificacaoAtualizacaoService();
      final informacoes = await service.obterInformacoesAtualizacao();

      if (informacoes != null) {
        // Verificar se há uma URL da loja válida
        final url = informacoes['urlLoja'] as String? ?? '';

        // Se a URL não estiver vazia, tentar exibir o diálogo
        if (url.isNotEmpty) {
          await service.exibirDialogAtualizacao(
            informacoes: informacoes,
            onAtualizar: () {
              launchUrl(Uri.parse(url), mode: LaunchMode.externalApplication);
            },
            onCancelar: () {
              // Ação cancelada pelo usuário
            },
          );
        }
      }
    } catch (e) {
      // Erro tratado silenciosamente
    }
  }
}

/// Widget de dialog personalizado para exibir informações de atualização
class TelaAtualizacaoDialog extends StatelessWidget {
  final String versaoAtual;
  final String versaoDisponivel;
  final String descricao;
  final String urlLoja;
  final bool isObrigatoria;
  final VoidCallback? onAtualizar;
  final VoidCallback? onCancelar;

  const TelaAtualizacaoDialog({
    super.key,
    required this.versaoAtual,
    required this.versaoDisponivel,
    required this.descricao,
    required this.urlLoja,
    this.isObrigatoria = false,
    this.onAtualizar,
    this.onCancelar,
  });

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
      ),
      title: Row(
        children: [
          Icon(
            Icons.system_update,
            color: isObrigatoria ? Colors.red : Colors.blue,
            size: 28,
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Text(
              isObrigatoria
                  ? 'Atualização Obrigatória'
                  : 'Atualização Disponível',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: isObrigatoria ? Colors.red : Colors.blue,
              ),
            ),
          ),
        ],
      ),
      content: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            descricao,
            style: const TextStyle(fontSize: 16),
          ),
          const SizedBox(height: 16),
          _buildInfoRow('Versão Atual Instalada:', versaoAtual),
          const SizedBox(height: 8),
          _buildInfoRow('Nova Versão:', versaoDisponivel),
          if (isObrigatoria) ...[
            const SizedBox(height: 16),
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.red.withOpacity(0.1),
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: Colors.red.withOpacity(0.3)),
              ),
              child: const Row(
                children: [
                  Icon(Icons.warning, color: Colors.red, size: 20),
                  SizedBox(width: 8),
                  Expanded(
                    child: Text(
                      'Esta atualização é obrigatória para continuar usando o aplicativo.',
                      style: TextStyle(
                        color: Colors.red,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ],
      ),
      actions: [
        if (!isObrigatoria)
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
              onCancelar?.call();
            },
            child: const Text('Mais Tarde'),
          ),
        ElevatedButton(
          onPressed: () {
            Navigator.of(context).pop();
            onAtualizar?.call();
            _abrirLoja();
          },
          style: ElevatedButton.styleFrom(
            backgroundColor: isObrigatoria ? Colors.red : Colors.blue,
            foregroundColor: Colors.white,
          ),
          child: const Text('Atualizar'),
        ),
      ],
    );
  }

  Widget _buildInfoRow(String label, String value) {
    return Row(
      children: [
        Text(
          label,
          style: const TextStyle(
            fontWeight: FontWeight.w500,
            color: Colors.grey,
          ),
        ),
        const SizedBox(width: 8),
        Text(
          value,
          style: const TextStyle(
            fontWeight: FontWeight.bold,
          ),
        ),
      ],
    );
  }

  void _abrirLoja() async {
    if (urlLoja.isNotEmpty) {
      try {
        final uri = Uri.parse(urlLoja);
        await launchUrl(uri, mode: LaunchMode.externalApplication);
      } catch (e) {
        // Erro tratado silenciosamente
      }
    }
  }
}
