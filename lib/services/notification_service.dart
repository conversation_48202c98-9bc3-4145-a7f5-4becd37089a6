import 'dart:convert';
import 'dart:developer';
import 'dart:io' show Platform;

import 'package:emartdriver/ui/home/<USER>/sound_notification.dart';
import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:flutter/material.dart';
import 'package:flutter_local_notifications/flutter_local_notifications.dart';

// Callback global para tratar cliques em notificações
typedef NotificationTapCallback = void Function(Map<String, dynamic> orderData);

Future<void> firebaseMessageBackgroundHandle(RemoteMessage message) async {
  // Background message handler
}

class NotificationService {
  FlutterLocalNotificationsPlugin flutterLocalNotificationsPlugin =
      FlutterLocalNotificationsPlugin();
  final GlobalKey<NavigatorState> navigatorKey = GlobalKey<NavigatorState>();

  // Callback para tratar cliques em notificações de pedidos
  static NotificationTapCallback? onOrderNotificationTap;

  void display(RemoteMessage message) async {
    try {
      // final id = DateTime.now().millisecondsSinceEpoch ~/ 1000;

      AndroidNotificationChannel channel = const AndroidNotificationChannel(
        "01",
        "Tá entregue",
        description: 'Show Tá entregue Notification',
        importance: Importance.max,
        audioAttributesUsage: AudioAttributesUsage.notification,
        playSound: true,
        sound: RawResourceAndroidNotificationSound('notification'),
      );
      AndroidNotificationDetails notificationDetails =
          AndroidNotificationDetails(channel.id, channel.name,
              channelDescription: 'your channel Description',
              importance: Importance.high,
              priority: Priority.high,
              ticker: 'ticker');
      const DarwinNotificationDetails darwinNotificationDetails =
          DarwinNotificationDetails(
              presentAlert: true, presentBadge: true, presentSound: true);
      NotificationDetails notificationDetailsBoth = NotificationDetails(
          android: notificationDetails, iOS: darwinNotificationDetails);
      await FlutterLocalNotificationsPlugin().show(
        0,
        message.notification!.title,
        message.notification!.body,
        notificationDetailsBoth,
        payload: jsonEncode(message.data),
      );
    } catch (e) {
      // Error handling notification display
    }
  }

  void handleNotificationTap(RemoteMessage message) {}

  /// Trata o clique em notificações locais
  void _onNotificationTap(NotificationResponse notificationResponse) {
    try {
      final payload = notificationResponse.payload;
      if (payload != null && payload.isNotEmpty) {
        // Decodificar payload simples
        final payloadData = jsonDecode(payload) as Map<String, dynamic>;

        // Verificar se é uma notificação de novo pedido
        if (payloadData['type'] == 'new_order' &&
            payloadData['orderId'] != null) {
          // Chamar callback se estiver definido
          if (onOrderNotificationTap != null) {
            onOrderNotificationTap!(payloadData);
          }
        }
      }
    } catch (e) {
      log('❌ [NOTIFICATION] Erro ao processar clique: $e');
    }
  }

  // Method to show new order notification with sound
  Future<void> showNewOrderNotification({
    required String orderId,
    required String vendorTitle,
    Map<String, dynamic>? orderData,
  }) async {
    try {
      const AndroidNotificationDetails androidPlatformChannelSpecifics =
          AndroidNotificationDetails(
        'new_order_channel',
        'Novos Pedidos',
        channelDescription: 'Notificações de novos pedidos disponíveis',
        importance: Importance.high,
        priority: Priority.high,
        enableVibration: true,
        enableLights: true,
        ledColor: Colors.orange,
        ledOnMs: 1000,
        ledOffMs: 500,
        playSound: false,
        styleInformation: DefaultStyleInformation(true, true),
      );

      const DarwinNotificationDetails iosPlatformChannelSpecifics =
          DarwinNotificationDetails(
        presentAlert: true,
        presentBadge: true,
        presentSound: true,
        presentBanner: true,
      );

      const NotificationDetails platformChannelSpecifics = NotificationDetails(
        android: androidPlatformChannelSpecifics,
        iOS: iosPlatformChannelSpecifics,
      );

      // Payload simples contendo apenas o ID do pedido
      final payload = jsonEncode({
        'orderId': orderId,
        'type': 'new_order',
        'timestamp': DateTime.now().toIso8601String(),
      });

      await flutterLocalNotificationsPlugin.show(
        DateTime.now().millisecondsSinceEpoch ~/ 1000,
        'Novo Pedido Disponível! 🛵',
        'Pedido da $vendorTitle Pedido: ${orderId.substring(0, 6)}',
        platformChannelSpecifics,
        payload: payload,
      );
      playSound();
    } catch (e) {
      log("❌ [NOTIFICATION] Erro ao exibir notificação: $e");
    }
  }

  initInfo() async {
    try {
      await FirebaseMessaging.instance
          .setForegroundNotificationPresentationOptions(
        alert: true,
        badge: true,
        sound: true,
      );

      var request = await FirebaseMessaging.instance.requestPermission(
        alert: true,
        announcement: false,
        badge: true,
        carPlay: false,
        criticalAlert: false,
        provisional: false,
        sound: true,
      );

      if (request.authorizationStatus == AuthorizationStatus.authorized ||
          request.authorizationStatus == AuthorizationStatus.provisional) {
        const AndroidInitializationSettings initializationSettingsAndroid =
            AndroidInitializationSettings('@mipmap/launcher_icon');
        var iosInitializationSettings = const DarwinInitializationSettings();
        final InitializationSettings initializationSettings =
            InitializationSettings(
                android: initializationSettingsAndroid,
                iOS: iosInitializationSettings);
        await flutterLocalNotificationsPlugin.initialize(initializationSettings,
            onDidReceiveNotificationResponse: _onNotificationTap);
        await setupInteractedMessage();
      }
    } catch (e) {
      // Error handling initialization
    }
  }

  Future<void> setupInteractedMessage() async {
    try {
      RemoteMessage? initialMessage =
          await FirebaseMessaging.instance.getInitialMessage();
      if (initialMessage != null) {
        FirebaseMessaging.onBackgroundMessage(
            (message) => firebaseMessageBackgroundHandle(message));
      }

      FirebaseMessaging.onMessage.listen((RemoteMessage message) {
        if (message.notification != null) {
          display(message);
        }
      });

      FirebaseMessaging.onMessageOpenedApp.listen((RemoteMessage message) {
        if (message.notification != null) {
          handleNotificationTap(message);
        }
      });

      if (Platform.isIOS) {
        String? apnsToken = await FirebaseMessaging.instance.getAPNSToken();
        if (apnsToken != null) {
          await FirebaseMessaging.instance.subscribeToTopic("ta_entregue");
        }
      } else {
        await FirebaseMessaging.instance.subscribeToTopic("ta_entregue");
      }
    } catch (e) {
      // Error handling setup messages
    }
  }

  static Future<String?> getToken() async {
    try {
      if (Platform.isIOS) {
        String? apnsToken = await FirebaseMessaging.instance.getAPNSToken();

        if (apnsToken == null) {
          for (int i = 0; i < 3; i++) {
            await Future.delayed(const Duration(seconds: 2));
            apnsToken = await FirebaseMessaging.instance.getAPNSToken();
            if (apnsToken != null) {
              break;
            }
          }
        }

        if (apnsToken != null) {
          String? fcmToken = await FirebaseMessaging.instance.getToken();
          return fcmToken;
        }

        return "temporary_token_for_ios";
      } else {
        return await FirebaseMessaging.instance.getToken();
      }
    } catch (e) {
      return "temporary_token_on_error";
    }
  }
}
