import 'dart:async';
import 'dart:developer';
import 'package:flutter/material.dart';
import 'package:geolocator/geolocator.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';

class LocationService {
  static final LocationService _instance = LocationService._internal();
  factory LocationService() => _instance;
  LocationService._internal();

  LatLng? _currentPosition;
  StreamSubscription<Position>? _positionStreamSubscription;
  
  // Callbacks para notificar mudanças
  Function(LatLng)? onPositionChanged;
  VoidCallback? onPositionError;

  LatLng? get currentPosition => _currentPosition;
  bool get hasPosition => _currentPosition != null;

  /// Determina a posição atual do usuário
  Future<LatLng?> determinePosition() async {
    try {
      bool serviceEnabled = await Geolocator.isLocationServiceEnabled();
      if (!serviceEnabled) {
        log("Location services not enabled");
        onPositionError?.call();
        return null;
      }

      LocationPermission permission = await Geolocator.checkPermission();
      if (permission == LocationPermission.denied) {
        permission = await Geolocator.requestPermission();
        if (permission == LocationPermission.deniedForever) {
          log("Location permission denied forever");
          onPositionError?.call();
          return null;
        }
      }

      final position = await Geolocator.getCurrentPosition(
          desiredAccuracy: LocationAccuracy.high);

      _currentPosition = LatLng(position.latitude, position.longitude);
      
      log("Current position determined: ${position.latitude}, ${position.longitude}");
      
      onPositionChanged?.call(_currentPosition!);
      
      return _currentPosition;
    } catch (e) {
      log("Error determining position: $e");
      onPositionError?.call();
      return null;
    }
  }

  /// Inicia o monitoramento contínuo da posição
  void startPositionUpdates() {
    _positionStreamSubscription?.cancel();

    const LocationSettings locationSettings = LocationSettings(
      accuracy: LocationAccuracy.high,
      distanceFilter: 10,
    );

    _positionStreamSubscription = Geolocator.getPositionStream(
      locationSettings: locationSettings,
    ).listen((Position position) {
      _currentPosition = LatLng(position.latitude, position.longitude);
      onPositionChanged?.call(_currentPosition!);
    });
  }

  /// Para o monitoramento da posição
  void stopPositionUpdates() {
    _positionStreamSubscription?.cancel();
    _positionStreamSubscription = null;
  }

  /// Centraliza o mapa na posição atual
  Future<void> centerOnCurrentLocation(GoogleMapController mapController, BuildContext context) async {
    if (_currentPosition == null) {
      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Aguardando localização...'),
            duration: Duration(seconds: 2),
          ),
        );
      }
      await determinePosition();
      return;
    }

    try {
      await mapController.animateCamera(
        CameraUpdate.newCameraPosition(
          CameraPosition(
            target: _currentPosition!,
            zoom: 17.0,
            tilt: 0,
          ),
        ),
      );
    } catch (e) {
      log("Erro ao centralizar mapa: $e");
    }
  }

  /// Calcula a distância entre dois pontos em quilômetros
  double calculateDistanceInKm(LatLng from, LatLng to) {
    return Geolocator.distanceBetween(
      from.latitude,
      from.longitude,
      to.latitude,
      to.longitude,
    ) / 1000;
  }

  /// Limpa os recursos
  void dispose() {
    _positionStreamSubscription?.cancel();
    _positionStreamSubscription = null;
    onPositionChanged = null;
    onPositionError = null;
  }
}
