import 'dart:async';
import 'dart:developer';
import 'dart:math' as math;

import 'package:emartdriver/main.dart';
import 'package:emartdriver/services/location_service.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';
import 'package:signalr_netcore/signalr_client.dart';

/// Configurações para o SignalR
class SignalRConfig {
  static const String baseUrl =
      'https://server-api-entregador.w4dxlp.easypanel.host';
  static const String hubEndpoint = '/entregadorhub';
  static const Duration reconnectDelay = Duration(seconds: 5);
  static const Duration connectionTimeout = Duration(seconds: 30);
  static const int maxReconnectAttempts = 5;
}

/// Exceção personalizada para erros do SignalR
class SignalRException implements Exception {
  final String message;
  final String? details;

  SignalRException(this.message, {this.details});

  @override
  String toString() =>
      'SignalRException: $message${details != null ? ' - $details' : ''}';
}

/// Serviço SignalR para comunicação em tempo real com o servidor
/// Monitora o status online do entregador e envia dados automaticamente
class SignalRService {
  static final SignalRService _instance = SignalRService._internal();
  factory SignalRService() => _instance;
  SignalRService._internal();

  // Conexão SignalR
  HubConnection? _hubConnection;
  bool _isConnected = false;
  bool _isConnecting = false;
  int _reconnectAttempts = 0;
  Timer? _heartbeatTimer;
  Timer? _dataUpdateTimer;

  // Estado do entregador
  bool _isOnline = false;
  String? _entregadorId;

  // Controle de localização
  LatLng? _lastSentLocation;
  DateTime? _lastSentTime;

  // Callbacks para eventos
  Function(Map<String, dynamic>)? onLocalizacaoAtualizada;
  Function(Map<String, dynamic>)? onEntregadorRegistrado;
  Function(String)? onErro;
  Function(bool)? onConnectionStateChanged;

  // Getters
  bool get isConnected => _isConnected;
  bool get isOnline => _isOnline;
  String? get entregadorId => _entregadorId;

  /// Inicializa o serviço SignalR
  Future<void> initialize() async {
    try {
      log('🔧 [SIGNALR] Inicializando serviço SignalR...');

      // Obter ID do entregador atual
      _entregadorId = MyAppState.currentUser?.userID;
      if (_entregadorId == null || _entregadorId!.isEmpty) {
        throw SignalRException('ID do entregador não disponível');
      }

      await _createConnection();
      await _setupEventHandlers();

      log('✅ [SIGNALR] Serviço inicializado com sucesso');
    } catch (e) {
      log('❌ [SIGNALR] Erro na inicialização: $e');
      throw SignalRException('Falha na inicialização', details: e.toString());
    }
  }

  /// Cria a conexão SignalR
  Future<void> _createConnection() async {
    try {
      const url = '${SignalRConfig.baseUrl}${SignalRConfig.hubEndpoint}';
      log('🔗 [SIGNALR] Criando conexão para: $url');

      _hubConnection = HubConnectionBuilder()
          .withUrl(url)
          .withAutomaticReconnect(retryDelays: [
        SignalRConfig.reconnectDelay.inMilliseconds,
        SignalRConfig.reconnectDelay.inMilliseconds * 2,
        SignalRConfig.reconnectDelay.inMilliseconds * 4,
      ]).build();

      log('✅ [SIGNALR] Conexão criada');
    } catch (e) {
      log('❌ [SIGNALR] Erro ao criar conexão: $e');
      rethrow;
    }
  }

  /// Configura os handlers de eventos do SignalR
  Future<void> _setupEventHandlers() async {
    if (_hubConnection == null) return;

    try {
      // Handler para localização atualizada
      _hubConnection!.on('LocalizacaoAtualizada', (arguments) {
        if (arguments != null && arguments.isNotEmpty) {
          final data = arguments[0] as Map<String, dynamic>;
          log('📍 [SIGNALR] Localização atualizada recebida: ${data['entregadorId']}');
          onLocalizacaoAtualizada?.call(data);
        }
      });

      // Handler para entregador registrado
      _hubConnection!.on('EntregadorRegistrado', (arguments) {
        if (arguments != null && arguments.isNotEmpty) {
          final data = arguments[0] as Map<String, dynamic>;
          log('👤 [SIGNALR] Entregador registrado: ${data['entregadorId']}');
          onEntregadorRegistrado?.call(data);
        }
      });

      // Handler para erros
      _hubConnection!.on('Erro', (arguments) {
        if (arguments != null && arguments.isNotEmpty) {
          final erro = arguments[0] as String;
          log('❌ [SIGNALR] Erro recebido do servidor: $erro');
          onErro?.call(erro);
        }
      });

      // Handler para mudanças de estado da conexão
      _hubConnection!.onclose(({Exception? error}) {
        log('🔌 [SIGNALR] Conexão fechada: ${error?.toString() ?? 'Sem erro'}');
        _isConnected = false;
        _isConnecting = false;
        _stopHeartbeat();
        _stopDataUpdates();
        onConnectionStateChanged?.call(false);
      });

      _hubConnection!.onreconnecting(({Exception? error}) {
        log('🔄 [SIGNALR] Reconectando: ${error?.toString() ?? 'Sem erro'}');
        _isConnected = false;
        _isConnecting = true;
        onConnectionStateChanged?.call(false);
      });

      _hubConnection!.onreconnected(({String? connectionId}) {
        log('✅ [SIGNALR] Reconectado com ID: ${connectionId ?? 'unknown'}');
        _isConnected = true;
        _isConnecting = false;
        _reconnectAttempts = 0;
        _startHeartbeat();
        if (_isOnline) {
          _startDataUpdates();
        }
        onConnectionStateChanged?.call(true);
      });

      log('✅ [SIGNALR] Event handlers configurados');
    } catch (e) {
      log('❌ [SIGNALR] Erro ao configurar event handlers: $e');
      rethrow;
    }
  }

  /// Conecta ao hub SignalR
  Future<bool> connect() async {
    if (_isConnected || _isConnecting) {
      log('⚠️ [SIGNALR] Já conectado ou conectando');
      return _isConnected;
    }

    if (_hubConnection == null) {
      log('❌ [SIGNALR] Conexão não inicializada');
      return false;
    }

    try {
      _isConnecting = true;
      log('🔗 [SIGNALR] Iniciando conexão...');

      await _hubConnection!.start();

      _isConnected = true;
      _isConnecting = false;
      _reconnectAttempts = 0;

      log('✅ [SIGNALR] Conectado com sucesso');

      _startHeartbeat();
      onConnectionStateChanged?.call(true);

      return true;
    } catch (e) {
      _isConnected = false;
      _isConnecting = false;
      _reconnectAttempts++;

      log('❌ [SIGNALR] Erro na conexão (tentativa $_reconnectAttempts): $e');

      // Tentar reconectar automaticamente
      if (_reconnectAttempts < SignalRConfig.maxReconnectAttempts) {
        log('🔄 [SIGNALR] Tentando reconectar em ${SignalRConfig.reconnectDelay.inSeconds}s...');
        Timer(SignalRConfig.reconnectDelay, () => connect());
      } else {
        log('❌ [SIGNALR] Máximo de tentativas de reconexão atingido');
        onErro?.call(
            'Falha na conexão após ${SignalRConfig.maxReconnectAttempts} tentativas');
      }

      return false;
    }
  }

  /// Desconecta do hub SignalR
  Future<void> disconnect() async {
    try {
      log('🔌 [SIGNALR] Desconectando...');

      _stopHeartbeat();
      _stopDataUpdates();

      if (_hubConnection != null && _isConnected) {
        await _hubConnection!.stop();
      }

      _isConnected = false;
      _isConnecting = false;
      _reconnectAttempts = 0;

      onConnectionStateChanged?.call(false);
      log('✅ [SIGNALR] Desconectado');
    } catch (e) {
      log('❌ [SIGNALR] Erro ao desconectar: $e');
    }
  }

  /// Atualiza o status online do entregador
  void updateOnlineStatus(bool isOnline) {
    log('📊 [SIGNALR] Atualizando status online: $isOnline');

    final wasOnline = _isOnline;
    _isOnline = isOnline;

    if (_isOnline && !wasOnline) {
      // Ficou online - iniciar envio de dados
      _startDataUpdates();
      log('🟢 [SIGNALR] Entregador ficou online - iniciando envio de dados');
    } else if (!_isOnline && wasOnline) {
      // Ficou offline - parar envio de dados
      _stopDataUpdates();
      log('🔴 [SIGNALR] Entregador ficou offline - parando envio de dados');
    }
  }

  /// Inicia o heartbeat para manter a conexão ativa (desabilitado)
  void _startHeartbeat() {
    _stopHeartbeat();

    // Heartbeat desabilitado pois o método não existe no servidor
    // A conexão SignalR já tem seu próprio mecanismo de keep-alive
    /*
    _heartbeatTimer = Timer.periodic(const Duration(minutes: 1), (timer) {
      if (_isConnected && _hubConnection != null) {
        _sendHeartbeat();
      }
    });
    */

    log('💓 [SIGNALR] Heartbeat desabilitado (não necessário)');
  }

  /// Para o heartbeat
  void _stopHeartbeat() {
    _heartbeatTimer?.cancel();
    _heartbeatTimer = null;
    log('💓 [SIGNALR] Heartbeat parado');
  }

  /// Inicia o envio automático de dados quando online
  void _startDataUpdates() {
    if (!_isOnline || _dataUpdateTimer != null) return;

    // Timer principal: verifica a cada 5 segundos se deve enviar
    // (força envio a cada 30s, ou envia se moveu 20m)
    _dataUpdateTimer = Timer.periodic(const Duration(seconds: 5), (timer) {
      if (_isOnline && _isConnected) {
        _sendLocationUpdate();
      }
    });

    log('📡 [SIGNALR] Envio automático de dados iniciado (30s ou 20m de distância)');
  }

  /// Para o envio automático de dados
  void _stopDataUpdates() {
    _dataUpdateTimer?.cancel();
    _dataUpdateTimer = null;
    log('📡 [SIGNALR] Envio automático de dados parado');
  }

  /// Envia atualização de localização com dados do entregador
  /// [forceUpdate] força o envio independente de tempo/distância
  Future<void> _sendLocationUpdate({bool forceUpdate = false}) async {
    try {
      if (!_isConnected || !_isOnline || _entregadorId == null) return;

      final locationService = LocationService();

      // Tentar obter localização atual se não estiver disponível
      var position = locationService.currentPosition;
      if (position == null) {
        log('📍 [SIGNALR] Obtendo localização atual...');
        position = await locationService.determinePosition();
      }

      if (position == null) {
        log('⚠️ [SIGNALR] Localização não disponível para envio');
        return;
      }

      final currentLocation = LatLng(position.latitude, position.longitude);
      final now = DateTime.now();

      // Verificar se deve enviar baseado em tempo ou distância
      bool shouldSend = forceUpdate;
      String reason = 'forçado';

      if (!shouldSend) {
        // Verificar tempo (30 segundos)
        if (_lastSentTime == null ||
            now.difference(_lastSentTime!).inSeconds >= 30) {
          shouldSend = true;
          reason = 'tempo (30s)';
        }

        // Verificar distância (20 metros)
        if (!shouldSend && _lastSentLocation != null) {
          final distance =
              _calculateDistance(_lastSentLocation!, currentLocation);
          if (distance >= 20.0) {
            shouldSend = true;
            reason = 'distância (${distance.toStringAsFixed(1)}m)';
          }
        }

        // Se é a primeira vez, enviar
        if (!shouldSend && _lastSentLocation == null) {
          shouldSend = true;
          reason = 'primeira localização';
        }
      }

      if (!shouldSend) {
        return; // Não precisa enviar ainda
      }

      // Obter dados do usuário atual
      final user = MyAppState.currentUser;
      final nome = user != null
          ? '${user.firstName} ${user.lastName}'.trim()
          : 'Entregador';
      final numero = user?.phoneNumber ?? '';

      await registrarEAtualizarLocalizacao(
        _entregadorId!,
        position.latitude,
        position.longitude,
        nome,
        numero,
      );

      // Atualizar controles
      _lastSentLocation = currentLocation;
      _lastSentTime = now;

      log('📍 [SIGNALR] Dados enviados ($reason):');
      log('   - Localização: ${position.latitude}, ${position.longitude}');
      log('   - Nome: $nome');
      log('   - Número: $numero');
    } catch (e) {
      log('❌ [SIGNALR] Erro no envio automático de dados: $e');
    }
  }

  /// Calcula a distância entre duas coordenadas em metros
  double _calculateDistance(LatLng point1, LatLng point2) {
    const double earthRadius = 6371000; // Raio da Terra em metros

    final double lat1Rad = point1.latitude * (math.pi / 180);
    final double lat2Rad = point2.latitude * (math.pi / 180);
    final double deltaLatRad =
        (point2.latitude - point1.latitude) * (math.pi / 180);
    final double deltaLngRad =
        (point2.longitude - point1.longitude) * (math.pi / 180);

    final double a = math.sin(deltaLatRad / 2) * math.sin(deltaLatRad / 2) +
        math.cos(lat1Rad) *
            math.cos(lat2Rad) *
            math.sin(deltaLngRad / 2) *
            math.sin(deltaLngRad / 2);

    final double c = 2 * math.atan2(math.sqrt(a), math.sqrt(1 - a));

    return earthRadius * c;
  }

  /// Atualiza a localização do entregador via SignalR
  Future<void> atualizarLocalizacao(
      String entregadorId, double lat, double lng) async {
    try {
      if (!_isConnected || _hubConnection == null) {
        throw SignalRException('SignalR não conectado');
      }

      await _hubConnection!
          .invoke('AtualizarLocalizacao', args: [entregadorId, lat, lng]);
      log('📍 [SIGNALR] Localização atualizada: $lat, $lng');
    } catch (e) {
      log('❌ [SIGNALR] Erro ao atualizar localização: $e');
      rethrow;
    }
  }

  /// Registra o entregador e atualiza sua localização
  Future<void> registrarEAtualizarLocalizacao(
    String entregadorId,
    double lat,
    double lng,
    String nome,
    String numero,
  ) async {
    try {
      if (!_isConnected || _hubConnection == null) {
        throw SignalRException('SignalR não conectado');
      }

      await _hubConnection!.invoke('RegistrarEAtualizarLocalizacao',
          args: [entregadorId, lat, lng, nome, numero]);

      log('👤 [SIGNALR] Entregador registrado: $nome ($numero)');
    } catch (e) {
      log('❌ [SIGNALR] Erro ao registrar entregador: $e');
      rethrow;
    }
  }

  /// Força uma atualização imediata de dados
  Future<void> forceUpdate() async {
    if (_isOnline && _isConnected) {
      await _sendLocationUpdate(forceUpdate: true);
    }
  }

  /// Limpa todos os recursos e para o serviço
  void dispose() {
    log('🧹 [SIGNALR] Fazendo dispose do serviço...');

    _stopHeartbeat();
    _stopDataUpdates();

    if (_hubConnection != null) {
      _hubConnection!.stop();
    }

    _isConnected = false;
    _isConnecting = false;
    _isOnline = false;
    _reconnectAttempts = 0;

    // Limpar callbacks
    onLocalizacaoAtualizada = null;
    onEntregadorRegistrado = null;
    onErro = null;
    onConnectionStateChanged = null;

    log('✅ [SIGNALR] Dispose concluído');
  }
}
