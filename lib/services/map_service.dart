import 'dart:async';
import 'dart:developer';
import 'dart:ui' as ui;

import 'package:flutter/services.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';

class MapService {
  static final MapService _instance = MapService._internal();
  factory MapService() => _instance;
  MapService._internal();

  GoogleMapController? _mapController;
  final Map<String, Marker> _markers = {};
  final Set<Polyline> _polylines = {};
  BitmapDescriptor? _deliveryPersonIcon;

  Map<String, Marker> get markers => _markers;
  Set<Polyline> get polylines => _polylines;
  GoogleMapController? get mapController => _mapController;

  /// Inicializa o controlador do mapa
  void setMapController(GoogleMapController controller) {
    _mapController = controller;
    log("Map controller set successfully");
  }

  /// Carrega o ícone customizado do entregador
  Future<void> loadCustomMarkerIcon() async {
    try {
      final Uint8List markerIcon =
          await getBytesFromAsset('assets/images/motoentregador.png', 40);
      _deliveryPersonIcon = BitmapDescriptor.bytes(markerIcon);

      _deliveryPersonIcon ??=
          BitmapDescriptor.defaultMarkerWithHue(BitmapDescriptor.hueAzure);

      log("Custom marker icon loaded successfully");
    } catch (e) {
      log("Error loading custom marker icon: $e");
      _deliveryPersonIcon =
          BitmapDescriptor.defaultMarkerWithHue(BitmapDescriptor.hueAzure);
    }
  }

  /// Converte asset em bytes para usar como ícone
  Future<Uint8List> getBytesFromAsset(String path, int width) async {
    ByteData data = await rootBundle.load(path);
    ui.Codec codec = await ui.instantiateImageCodec(data.buffer.asUint8List(),
        targetWidth: width);
    ui.FrameInfo fi = await codec.getNextFrame();
    return (await fi.image.toByteData(format: ui.ImageByteFormat.png))!
        .buffer
        .asUint8List();
  }

  /// Atualiza o marcador da posição atual do entregador
  void updateCurrentPositionMarker(LatLng position) {
    if (_deliveryPersonIcon == null) return;

    _markers['entregador'] = Marker(
      markerId: const MarkerId('entregador'),
      position: position,
      icon: _deliveryPersonIcon!,
      rotation: position.latitude,
    );
  }

  /// Adiciona um marcador no mapa
  void addMarker(String id, Marker marker) {
    _markers[id] = marker;
  }

  /// Remove um marcador do mapa
  void removeMarker(String id) {
    _markers.remove(id);
  }

  /// Limpa todos os marcadores exceto o da posição atual
  void clearMarkersExceptCurrentPosition() {
    final currentPositionMarker = _markers['entregador'];
    _markers.clear();
    if (currentPositionMarker != null) {
      _markers['entregador'] = currentPositionMarker;
    }
  }

  /// Limpa apenas marcadores de pedidos (mantém entregador e destino)
  void clearOrderMarkers() {
    // Remover apenas marcadores de lojas (que começam com 'loja_')
    final markersToRemove = <String>[];
    for (final key in _markers.keys) {
      if (key.startsWith('loja_')) {
        markersToRemove.add(key);
      }
    }

    for (final key in markersToRemove) {
      _markers.remove(key);
    }
  }

  /// Limpa todos os marcadores
  void clearAllMarkers() {
    _markers.clear();
  }

  /// Adiciona uma polyline no mapa
  void addPolyline(Polyline polyline) {
    _polylines.add(polyline);
  }

  /// Remove uma polyline do mapa
  void removePolyline(String polylineId) {
    _polylines
        .removeWhere((polyline) => polyline.polylineId.value == polylineId);
  }

  /// Limpa todas as polylines
  void clearPolylines() {
    _polylines.clear();
  }

  /// Aumenta o zoom do mapa
  Future<void> zoomIn() async {
    if (_mapController == null) return;

    try {
      final currentZoom = await _mapController!.getZoomLevel();
      double newZoom = currentZoom + 1.0;
      newZoom = newZoom > 20.0 ? 20.0 : newZoom;

      await _mapController!.animateCamera(
        CameraUpdate.zoomTo(newZoom),
      );
    } catch (e) {
      log("Erro ao aumentar zoom: $e");
    }
  }

  /// Diminui o zoom do mapa
  Future<void> zoomOut() async {
    if (_mapController == null) return;

    try {
      final currentZoom = await _mapController!.getZoomLevel();
      double newZoom = currentZoom - 1.0;
      newZoom = newZoom < 2.0 ? 2.0 : newZoom;

      await _mapController!.animateCamera(
        CameraUpdate.zoomTo(newZoom),
      );
    } catch (e) {
      log("Erro ao diminuir zoom: $e");
    }
  }

  /// Move a câmera para uma posição específica
  Future<void> animateToPosition(LatLng position, {double zoom = 15.0}) async {
    if (_mapController == null) return;

    try {
      await _mapController!.animateCamera(
        CameraUpdate.newLatLngZoom(position, zoom),
      );
    } catch (e) {
      log("Erro ao mover câmera: $e");
    }
  }

  /// Força o recarregamento do mapa
  void forceReload() {
    log("Forçando recarregamento do mapa");
    _markers.clear();
    _polylines.clear();
  }

  /// Limpa os recursos
  void dispose() {
    _markers.clear();
    _polylines.clear();
    _mapController = null;
    _deliveryPersonIcon = null;
  }
}
