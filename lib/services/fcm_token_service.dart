import 'dart:developer';
import 'dart:io' show Platform;

import 'package:firebase_messaging/firebase_messaging.dart';

/// Service responsável por gerenciar o FCM Token
/// Centraliza toda a lógica de obtenção e atualização do token FCM
class FcmTokenService {
  static final FcmTokenService _instance = FcmTokenService._internal();
  factory FcmTokenService() => _instance;
  FcmTokenService._internal();

  static const String _temporaryTokenIOS = 'temporary_token_for_ios';
  static const String _temporaryTokenError = 'temporary_token_on_error';

  /// Obtém o FCM token de forma segura
  /// Trata especificamente o caso do iOS onde é necessário aguardar o APNS token
  static Future<String?> getToken() async {
    try {
      log('🔑 [FCM_TOKEN] Iniciando obtenção do token');

      if (Platform.isIOS) {
        return await _getIOSToken();
      } else {
        return await _getAndroidToken();
      }
    } catch (e) {
      log('❌ [FCM_TOKEN] Erro ao obter token: $e');
      return _temporaryTokenError;
    }
  }

  /// Obtém o token FCM para iOS
  /// Aguarda o APNS token estar disponível antes de solicitar o FCM token
  static Future<String?> _getIOSToken() async {
    try {
      log('🍎 [FCM_TOKEN] Obtendo token para iOS');

      // Primeiro, verificar se o APNS token está disponível
      String? apnsToken = await FirebaseMessaging.instance.getAPNSToken();

      // Se não estiver disponível, tentar algumas vezes com delay
      if (apnsToken == null) {
        log('⏳ [FCM_TOKEN] APNS token não disponível, tentando novamente...');

        for (int i = 0; i < 3; i++) {
          await Future.delayed(const Duration(seconds: 2));
          apnsToken = await FirebaseMessaging.instance.getAPNSToken();

          if (apnsToken != null) {
            log('✅ [FCM_TOKEN] APNS token obtido na tentativa ${i + 1}');
            break;
          }
        }
      }

      // Se conseguiu obter o APNS token, solicitar o FCM token
      if (apnsToken != null) {
        String? fcmToken = await FirebaseMessaging.instance.getToken();
        log('✅ [FCM_TOKEN] Token FCM obtido para iOS: ${fcmToken?.substring(0, 20)}...');
        return fcmToken;
      }

      // Se não conseguiu obter o APNS token, retornar token temporário
      log('⚠️ [FCM_TOKEN] APNS token não disponível, usando token temporário');
      return _temporaryTokenIOS;
    } catch (e) {
      log('❌ [FCM_TOKEN] Erro ao obter token iOS: $e');
      return _temporaryTokenError;
    }
  }

  /// Obtém o token FCM para Android
  static Future<String?> _getAndroidToken() async {
    try {
      log('🤖 [FCM_TOKEN] Obtendo token para Android');
      String? fcmToken = await FirebaseMessaging.instance.getToken();
      log('✅ [FCM_TOKEN] Token FCM obtido para Android: ${fcmToken?.substring(0, 20)}...');
      return fcmToken;
    } catch (e) {
      log('❌ [FCM_TOKEN] Erro ao obter token Android: $e');
      return _temporaryTokenError;
    }
  }

  /// Força a atualização do token FCM
  /// Útil quando o token precisa ser renovado
  static Future<String?> refreshToken() async {
    try {
      log('🔄 [FCM_TOKEN] Forçando atualização do token');

      // Deletar o token atual (força renovação)
      await FirebaseMessaging.instance.deleteToken();

      // Aguardar um pouco antes de solicitar novo token
      await Future.delayed(const Duration(milliseconds: 500));

      // Obter novo token
      return await getToken();
    } catch (e) {
      log('❌ [FCM_TOKEN] Erro ao renovar token: $e');
      return null;
    }
  }

  /// Verifica se o token é válido (não é um token temporário)
  static bool isValidToken(String? token) {
    if (token == null || token.isEmpty) {
      return false;
    }

    return token != _temporaryTokenIOS && token != _temporaryTokenError;
  }

  /// Verifica se o token é temporário
  static bool isTemporaryToken(String? token) {
    if (token == null || token.isEmpty) {
      return false;
    }

    return token == _temporaryTokenIOS || token == _temporaryTokenError;
  }

  /// Configura o listener para mudanças no token
  /// Retorna um Stream que emite novos tokens quando eles mudam
  static Stream<String> onTokenRefresh() {
    return FirebaseMessaging.instance.onTokenRefresh;
  }

  /// Subscreve a um tópico FCM
  static Future<void> subscribeToTopic(String topic) async {
    try {
      log('📢 [FCM_TOKEN] Subscrevendo ao tópico: $topic');
      await FirebaseMessaging.instance.subscribeToTopic(topic);
      log('✅ [FCM_TOKEN] Subscrito ao tópico: $topic');
    } catch (e) {
      log('❌ [FCM_TOKEN] Erro ao subscrever ao tópico $topic: $e');
    }
  }

  /// Remove subscrição de um tópico FCM
  static Future<void> unsubscribeFromTopic(String topic) async {
    try {
      log('📢 [FCM_TOKEN] Removendo subscrição do tópico: $topic');
      await FirebaseMessaging.instance.unsubscribeFromTopic(topic);
      log('✅ [FCM_TOKEN] Subscrição removida do tópico: $topic');
    } catch (e) {
      log('❌ [FCM_TOKEN] Erro ao remover subscrição do tópico $topic: $e');
    }
  }

  /// Obtém informações sobre o token atual
  static Future<Map<String, dynamic>> getTokenInfo() async {
    final token = await getToken();

    return {
      'token': token,
      'isValid': isValidToken(token),
      'isTemporary': isTemporaryToken(token),
      'platform': Platform.isIOS ? 'iOS' : 'Android',
      'timestamp': DateTime.now().toIso8601String(),
    };
  }

  /// Inicializa o serviço FCM
  /// Configura permissões e configurações iniciais
  static Future<void> initialize() async {
    try {
      log('🚀 [FCM_TOKEN] Inicializando serviço FCM');

      // Solicitar permissões de notificação
      NotificationSettings settings =
          await FirebaseMessaging.instance.requestPermission(
        alert: true,
        announcement: false,
        badge: true,
        carPlay: false,
        criticalAlert: false,
        provisional: false,
        sound: true,
      );

      log('📱 [FCM_TOKEN] Permissões: ${settings.authorizationStatus}');

      // Configurar apresentação de notificações em foreground
      await FirebaseMessaging.instance
          .setForegroundNotificationPresentationOptions(
        alert: true,
        badge: true,
        sound: true,
      );

      // Para iOS, aguardar APNS token estar disponível
      if (Platform.isIOS) {
        String? apnsToken = await FirebaseMessaging.instance.getAPNSToken();
        if (apnsToken == null) {
          log('⏳ [FCM_TOKEN] Aguardando APNS token...');
          await Future.delayed(const Duration(seconds: 2));
        }
      }

      // Subscrever ao tópico padrão
      await subscribeToTopic("ta_entregue");

      log('✅ [FCM_TOKEN] Serviço FCM inicializado com sucesso');
    } catch (e) {
      log('❌ [FCM_TOKEN] Erro ao inicializar serviço FCM: $e');
    }
  }
}
