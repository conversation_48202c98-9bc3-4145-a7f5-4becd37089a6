import 'dart:developer';

/// Service para controlar notificações duplicadas
/// Evita que o mesmo pedido seja notificado múltiplas vezes dentro de um período
class NotificationThrottleService {
  static final NotificationThrottleService _instance =
      NotificationThrottleService._internal();
  factory NotificationThrottleService() => _instance;
  NotificationThrottleService._internal();

  // Mapa para armazenar quando cada pedido foi notificado pela última vez
  final Map<String, DateTime> _lastNotificationTime = {};

  // Tempo mínimo entre notificações do mesmo pedido (5 minutos)
  static const Duration _throttleDuration = Duration(minutes: 5);

  /// Verifica se um pedido pode ser notificado
  /// Retorna true se pode notificar, false se deve ser bloqueado
  bool canNotifyOrder(String orderId) {
    final now = DateTime.now();
    final lastNotification = _lastNotificationTime[orderId];

    if (lastNotification == null) {
      // Primeira notificação deste pedido
      log("✅ [THROTTLE] Primeira notificação do pedido: $orderId");
      return true;
    }

    final timeSinceLastNotification = now.difference(lastNotification);

    if (timeSinceLastNotification >= _throttleDuration) {
      // Tempo suficiente passou desde a última notificação
      log("✅ [THROTTLE] Tempo suficiente passou para pedido: $orderId (${timeSinceLastNotification.inMinutes} min)");
      return true;
    }

    // Muito cedo para notificar novamente
    final remainingTime = _throttleDuration - timeSinceLastNotification;
    log("⏰ [THROTTLE] Bloqueando notificação duplicada do pedido: $orderId (faltam ${remainingTime.inMinutes} min)");
    return false;
  }

  /// Registra que um pedido foi notificado
  void markOrderAsNotified(String orderId) {
    final now = DateTime.now();
    _lastNotificationTime[orderId] = now;
    log("📝 [THROTTLE] Pedido $orderId marcado como notificado às ${now.toString()}");
  }

  /// Remove um pedido do controle (quando aceito ou cancelado)
  void removeOrder(String orderId) {
    _lastNotificationTime.remove(orderId);
    log("🗑️ [THROTTLE] Pedido $orderId removido do controle");
  }

  /// Limpa notificações antigas (mais de 1 hora)
  void cleanupOldNotifications() {
    final now = DateTime.now();
    final cutoffTime = now.subtract(const Duration(hours: 1));

    final keysToRemove = <String>[];

    _lastNotificationTime.forEach((orderId, notificationTime) {
      if (notificationTime.isBefore(cutoffTime)) {
        keysToRemove.add(orderId);
      }
    });

    for (final key in keysToRemove) {
      _lastNotificationTime.remove(key);
    }

    if (keysToRemove.isNotEmpty) {
      log("🧹 [THROTTLE] Limpeza: ${keysToRemove.length} notificações antigas removidas");
    }
  }

  /// Obtém estatísticas do serviço
  Map<String, dynamic> getStats() {
    final now = DateTime.now();
    int recentNotifications = 0;

    _lastNotificationTime.forEach((orderId, notificationTime) {
      if (now.difference(notificationTime) <= _throttleDuration) {
        recentNotifications++;
      }
    });

    return {
      'totalTrackedOrders': _lastNotificationTime.length,
      'recentNotifications': recentNotifications,
      'throttleDurationMinutes': _throttleDuration.inMinutes,
    };
  }

  /// Força a limpeza de um pedido específico (para testes)
  void forceRemoveOrder(String orderId) {
    _lastNotificationTime.remove(orderId);
    log("🔧 [THROTTLE] Pedido $orderId removido forçadamente");
  }

  /// Limpa todos os dados (para testes ou reset)
  void clearAll() {
    _lastNotificationTime.clear();
    log("🔄 [THROTTLE] Todos os dados limpos");
  }

  /// Verifica se um pedido está sendo rastreado
  bool isOrderTracked(String orderId) {
    return _lastNotificationTime.containsKey(orderId);
  }

  /// Obtém o tempo da última notificação de um pedido
  DateTime? getLastNotificationTime(String orderId) {
    return _lastNotificationTime[orderId];
  }

  /// Obtém o tempo restante até poder notificar novamente
  Duration? getTimeUntilNextNotification(String orderId) {
    final lastNotification = _lastNotificationTime[orderId];
    if (lastNotification == null) return null;

    final now = DateTime.now();
    final timeSinceLastNotification = now.difference(lastNotification);

    if (timeSinceLastNotification >= _throttleDuration) {
      return Duration.zero;
    }

    return _throttleDuration - timeSinceLastNotification;
  }
}
