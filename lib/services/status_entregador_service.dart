import 'dart:async';
import 'dart:io';

import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:emartdriver/constants.dart';
import 'package:emartdriver/firebase_options.dart';
import 'package:emartdriver/main.dart';
import 'package:emartdriver/model/OrderModel.dart';
import 'package:emartdriver/services/notification_service.dart';
import 'package:emartdriver/services/notification_throttle_service.dart';
import 'package:emartdriver/services/signalr_service.dart';
import 'package:emartdriver/userPrefrence.dart';
import 'package:firebase_core/firebase_core.dart';
import 'package:flutter_foreground_task/flutter_foreground_task.dart';
import 'package:geolocator/geolocator.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';

// Callback para inicializar o TaskHandler
@pragma('vm:entry-point')
void startStatusCallback() {
  FlutterForegroundTask.setTaskHandler(StatusTaskHandler());
}

// TaskHandler para gerenciar o status do entregador em background
class StatusTaskHandler extends TaskHandler {
  static const String _notificationTitle = 'Taliso - Status do Entregador';

  // Order monitoring properties
  StreamSubscription<QuerySnapshot>? _ordersSubscription;
  Position? _currentPosition;
  bool _isOnline = false;
  String? _currentUserId;

  @override
  Future<void> onStart(DateTime timestamp, TaskStarter starter) async {
    print('🚀 [TASK_HANDLER] ===== INICIANDO STATUS TASK HANDLER =====');
    print('🚀 [TASK_HANDLER] Timestamp: $timestamp');
    print('🚀 [TASK_HANDLER] Starter: $starter');

    try {
      print('🚀 [TASK_HANDLER] Inicializando Firebase...');
      await Firebase.initializeApp(
        options: DefaultFirebaseOptions.currentPlatform,
      );
      print('✅ [TASK_HANDLER] Firebase inicializado com sucesso');
    } catch (e) {
      print('⚠️ [TASK_HANDLER] Firebase já inicializado ou erro: $e');
    }

    print('🚀 [TASK_HANDLER] Enviando confirmação de início para UI...');
    FlutterForegroundTask.sendDataToMain({
      'action': 'task_started',
      'timestamp': timestamp.millisecondsSinceEpoch,
    });

    // TESTE: Enviar dados de teste imediatamente
    print('🧪 [TASK_HANDLER] Enviando dados de teste inicial...');
    FlutterForegroundTask.sendDataToMain({
      'action': 'test_data',
      'message': 'TaskHandler iniciado e funcionando!',
      'timestamp': timestamp.millisecondsSinceEpoch,
    });

    print('✅ [TASK_HANDLER] StatusTaskHandler iniciado com sucesso!');
  }

  @override
  void onRepeatEvent(DateTime timestamp) {
    print('🔄 [REPEAT_EVENT] ===== EXECUTANDO CICLO DE ATUALIZAÇÃO =====');
    print('🔄 [REPEAT_EVENT] Timestamp: $timestamp');
    print('🔄 [REPEAT_EVENT] Status online atual: $_isOnline');

    _updateStatusAndLocation();

    // Fazer limpeza periódica do throttle service (a cada 10 minutos)
    if (timestamp.minute % 10 == 0) {
      final throttleService = NotificationThrottleService();
      throttleService.cleanupOldNotifications();
    }
  }

  @override
  Future<void> onDestroy(DateTime timestamp, bool isTimeout) async {
    print(
        'StatusTaskHandler destruído: ${timestamp.toString()}, timeout: $isTimeout');

    // Notificar a UI que o serviço foi parado
    FlutterForegroundTask.sendDataToMain({
      'action': 'task_destroyed',
      'timestamp': timestamp.millisecondsSinceEpoch,
      'isTimeout': isTimeout,
    });
  }

  @override
  void onReceiveData(Object data) {
    if (data is Map<String, dynamic>) {
      final action = data['action'] as String?;
      print('📨 [TASK_HANDLER] Comando recebido: $action');

      switch (action) {
        case 'update_status':
          final isOnline = data['isOnline'] as bool? ?? false;
          _updateOnlineStatus(isOnline);
          break;
        case 'update_location':
          _updateLocation();
          break;
        case 'force_update':
          _updateStatusAndLocation();
          break;
        case 'force_orders_update':
          print('🧪 [TASK_HANDLER] Forçando atualização de pedidos...');
          if (_isOnline) {
            _startUnifiedOrderMonitoring();
          } else {
            print(
                '🧪 [TASK_HANDLER] Usuário offline, não pode forçar atualização');
          }
          break;
      }
    }
  }

  @override
  void onNotificationButtonPressed(String id) {
    switch (id) {
      case 'btn_toggle_status':
        FlutterForegroundTask.sendDataToMain({
          'action': 'stop_service_requested',
        });
        break;
      case 'btn_update_location':
        _updateLocation();
        break;
    }
  }

  @override
  void onNotificationPressed() {
    FlutterForegroundTask.sendDataToMain({
      'action': 'notification_pressed',
    });
  }

  Future<void> _updateStatusAndLocation() async {
    print('🔄 [UPDATE_CYCLE] Iniciando ciclo de atualização...');
    try {
      print('📍 [UPDATE_CYCLE] Atualizando localização...');
      await _updateLocation();

      print('📊 [UPDATE_CYCLE] Atualizando status online...');
      await _updateOnlineStatus(null); // Mantém o status atual

      print('✅ [UPDATE_CYCLE] Ciclo de atualização concluído com sucesso');
    } catch (e) {
      print('❌ [UPDATE_CYCLE] Erro no ciclo de atualização: $e');
    }
  }

  Future<void> _updateOnlineStatus(bool? isOnline) async {
    try {
      final userId =
          await FlutterForegroundTask.getData(key: 'user_id') as String?;

      if (userId == null || userId.isEmpty) {
        return;
      }

      final currentStatus =
          await FlutterForegroundTask.getData(key: 'is_online') as bool? ??
              false;
      final statusToUpdate = isOnline ?? currentStatus;

      final dados = {
        'entregador_id': userId,
        'lastActive': Timestamp.now(),
        'isOnline': statusToUpdate,
        'updatedAt': Timestamp.now(),
      };

      await FirebaseFirestore.instance
          .collection('delivery_men_status')
          .doc(userId)
          .set(dados, SetOptions(merge: true));

      await FlutterForegroundTask.saveData(
          key: 'is_online', value: statusToUpdate);

      // Atualizar notificação
      final statusText = statusToUpdate ? 'Online' : 'Offline';
      FlutterForegroundTask.updateService(
        notificationTitle: _notificationTitle,
        notificationText: 'Status: $statusText',
      );

      // Update order monitoring based on online status
      print(
          '🔄 [STATUS_UPDATE] Status atual: $_isOnline, Novo status: $statusToUpdate');
      if (statusToUpdate != _isOnline) {
        print('🔄 [STATUS_UPDATE] Status mudou! Atualizando monitoramento...');
        _isOnline = statusToUpdate;
        if (_isOnline) {
          print(
              '🟢 [STATUS_UPDATE] Usuário ficou ONLINE - Iniciando monitoramento de pedidos');
          _startUnifiedOrderMonitoring();
        } else {
          print(
              '🔴 [STATUS_UPDATE] Usuário ficou OFFLINE - Parando monitoramento de pedidos');
          _stopOrderMonitoring();
        }
      } else {
        print('ℹ️ [STATUS_UPDATE] Status não mudou, mantendo estado atual');
      }

      // Enviar dados para a UI
      FlutterForegroundTask.sendDataToMain({
        'action': 'status_updated',
        'isOnline': statusToUpdate,
        'timestamp': DateTime.now().millisecondsSinceEpoch,
      });
    } catch (e) {
      print('Erro ao atualizar status online: $e');
    }
  }

  Future<void> _updateLocation() async {
    try {
      print('Iniciando _updateLocation...');
      final userId =
          await FlutterForegroundTask.getData(key: 'user_id') as String?;
      print('UserId para localização: $userId');

      if (userId == null || userId.isEmpty) {
        print('UserId é nulo ou vazio para localização, retornando...');
        return;
      }

      // Verificar permissões de localização
      print('Verificando permissões de localização...');
      final permission = await Geolocator.checkPermission();
      if (permission == LocationPermission.denied ||
          permission == LocationPermission.deniedForever) {
        print('Permissão de localização negada: $permission');
        return;
      }
      print('Permissão de localização OK: $permission');

      print('Obtendo posição atual...');
      final position = await Geolocator.getCurrentPosition(
        locationSettings: const LocationSettings(
          accuracy: LocationAccuracy.high,
          timeLimit: Duration(seconds: 10),
        ),
      );
      print('Posição obtida: ${position.latitude}, ${position.longitude}');

      final dados = {
        'entregador_id': userId,
        'location': {
          'latitude': position.latitude,
          'longitude': position.longitude,
        },
        'lastLocationUpdate': Timestamp.now(),
        'updatedAt': Timestamp.now(),
      };
      print('Dados de localização preparados: $dados');

      print('Enviando localização para Firebase...');
      await FirebaseFirestore.instance
          .collection('delivery_men_status')
          .doc(userId)
          .set(dados, SetOptions(merge: true));
      print('Localização enviada para Firebase com sucesso!');

      // Salvar localização localmente
      await FlutterForegroundTask.saveData(
          key: 'last_latitude', value: position.latitude);
      await FlutterForegroundTask.saveData(
          key: 'last_longitude', value: position.longitude);

      // Enviar dados para a UI
      FlutterForegroundTask.sendDataToMain({
        'action': 'location_updated',
        'latitude': position.latitude,
        'longitude': position.longitude,
        'timestamp': DateTime.now().millisecondsSinceEpoch,
      });
    } catch (e) {
      print('Erro ao atualizar localização: $e');
    }
  }

  // Order monitoring methods
  void _stopOrderMonitoring() {
    print('🛑 [ORDER_MONITORING] Parando monitoramento de pedidos...');
    _ordersSubscription?.cancel();
    _ordersSubscription = null;
    print('🛑 [ORDER_MONITORING] Monitoramento de pedidos parado com sucesso');
  }

  /// FUNÇÃO UNIFICADA DE LEITURA E NOTIFICAÇÃO DE PEDIDOS
  /// Esta é a única função responsável por ler pedidos e notificar sobre mudanças
  void _startUnifiedOrderMonitoring() async {
    print(
        '🚀 [UNIFIED_MONITORING] ===== INICIANDO MONITORAMENTO UNIFICADO =====');
    print('🚀 [UNIFIED_MONITORING] Status online: $_isOnline');

    _ordersSubscription?.cancel();

    if (!_isOnline) {
      print(
          '❌ [UNIFIED_MONITORING] Usuário offline, não iniciando monitoramento');
      return;
    }

    try {
      // Obter ID do usuário atual
      final userId =
          await FlutterForegroundTask.getData(key: 'user_id') as String?;
      if (userId == null || userId.isEmpty) {
        print('❌ [UNIFIED_MONITORING] UserID não encontrado');
        return;
      }

      print('🔍 [UNIFIED_MONITORING] Configurando queries Firebase...');

      // Query 1: Pedidos disponíveis para aceitar
      final availableOrdersQuery = FirebaseFirestore.instance
          .collection('vendor_orders')
          .where('status', isEqualTo: 'Procurando entregador');

      // Query 3: Pedidos de devolução
      final returnOrdersQuery = FirebaseFirestore.instance
          .collection('vendor_orders')
          .where('entregador_id', isEqualTo: userId)
          .where('status', isEqualTo: 'Entregue')
          .where('has_return', isEqualTo: true);

      print(
          '📊 [UNIFIED_MONITORING] Iniciando monitoramento de todos os tipos de pedidos...');

      // Listener unificado para pedidos disponíveis
      _ordersSubscription = availableOrdersQuery.snapshots().listen((snapshot) {
        _processAvailableOrders(snapshot);
      }, onError: (e) {
        print(
            "❌ [UNIFIED_MONITORING] Erro no listener de pedidos disponíveis: $e");
      });

      // Listener para pedidos de devolução
      returnOrdersQuery.snapshots().listen((snapshot) {
        _processReturnOrders(snapshot);
      }, onError: (e) {
        print(
            "❌ [UNIFIED_MONITORING] Erro no listener de pedidos de devolução: $e");
      });

      print(
          '✅ [UNIFIED_MONITORING] Monitoramento unificado iniciado com sucesso!');
    } catch (e) {
      print(
          "❌ [UNIFIED_MONITORING] Erro ao iniciar monitoramento unificado: $e");
    }
  }

  /// Processa pedidos disponíveis para aceitar
  void _processAvailableOrders(QuerySnapshot querySnapshot) {
    print(
        '📋 [AVAILABLE_ORDERS] Processando ${querySnapshot.docs.length} pedidos disponíveis...');

    try {
      if (!_isOnline) {
        print("⚠️ [AVAILABLE_ORDERS] Usuário offline, ignorando atualização");
        return;
      }

      List<Map<String, dynamic>> ordersData = [];

      for (var doc in querySnapshot.docs) {
        final data = doc.data() as Map<String, dynamic>;
        final orderId = data['id'] ?? doc.id;

        // Converter Timestamps para strings serializáveis
        final serializableData = _convertTimestampsToStrings(data);

        ordersData.add({
          'id': orderId,
          'docId': doc.id,
          'data': serializableData,
          'type': 'available'
        });

        _checkForNewOrder(data);
      }

      // Enviar dados para a UI
      FlutterForegroundTask.sendDataToMain({
        'action': 'available_orders_updated',
        'orders': ordersData,
        'timestamp': DateTime.now().millisecondsSinceEpoch,
      });

      print('✅ [AVAILABLE_ORDERS] ${ordersData.length} pedidos processados');
    } catch (e) {
      print("❌ [AVAILABLE_ORDERS] Erro ao processar: $e");
    }
  }

  /// Processa pedidos aceitos pelo entregador
  void _processAcceptedOrders(QuerySnapshot querySnapshot) {
    print(
        '📋 [ACCEPTED_ORDERS] Processando ${querySnapshot.docs.length} pedidos aceitos...');

    try {
      List<Map<String, dynamic>> ordersData = [];

      for (var doc in querySnapshot.docs) {
        final data = doc.data() as Map<String, dynamic>;
        final orderId = data['id'] ?? doc.id;

        // Converter Timestamps para strings serializáveis
        final serializableData = _convertTimestampsToStrings(data);

        ordersData.add({
          'id': orderId,
          'docId': doc.id,
          'data': serializableData,
          'type': 'accepted'
        });
      }

      // Enviar dados para a UI
      FlutterForegroundTask.sendDataToMain({
        'action': 'accepted_orders_updated',
        'orders': ordersData,
        'timestamp': DateTime.now().millisecondsSinceEpoch,
      });

      print('✅ [ACCEPTED_ORDERS] ${ordersData.length} pedidos processados');
    } catch (e) {
      print("❌ [ACCEPTED_ORDERS] Erro ao processar: $e");
    }
  }

  /// Processa pedidos de devolução
  void _processReturnOrders(QuerySnapshot querySnapshot) {
    print(
        '📋 [RETURN_ORDERS] Processando ${querySnapshot.docs.length} pedidos de devolução...');

    try {
      List<Map<String, dynamic>> ordersData = [];

      for (var doc in querySnapshot.docs) {
        final data = doc.data() as Map<String, dynamic>;
        final orderId = data['id'] ?? doc.id;

        // Converter Timestamps para strings serializáveis
        final serializableData = _convertTimestampsToStrings(data);

        ordersData.add({
          'id': orderId,
          'docId': doc.id,
          'data': serializableData,
          'type': 'return'
        });
      }

      // Enviar dados para a UI
      FlutterForegroundTask.sendDataToMain({
        'action': 'return_orders_updated',
        'orders': ordersData,
        'timestamp': DateTime.now().millisecondsSinceEpoch,
      });

      print('✅ [RETURN_ORDERS] ${ordersData.length} pedidos processados');
    } catch (e) {
      print("❌ [RETURN_ORDERS] Erro ao processar: $e");
    }
  }

  void _checkForNewOrder(Map<String, dynamic> orderData) async {
    final orderId = orderData['id'] ?? 'ID_DESCONHECIDO';
    print('🔍 [NEW_ORDER_CHECK] Verificando pedido: $orderId');

    try {
      final vendor = OrderModel.fromJson(orderData);
      final lojaPos = LatLng(
        vendor.vendor.address_store?.location.geoPoint.latitude ?? 0.0,
        vendor.vendor.address_store?.location.geoPoint.longitude ?? 0.0,
      );
      _currentPosition ??= await Geolocator.getCurrentPosition();
      final distancia = Geolocator.distanceBetween(
            _currentPosition?.latitude ?? 0.0,
            _currentPosition?.longitude ?? 0.0,
            lojaPos.latitude,
            lojaPos.longitude,
          ) /
          1000;
      if (distancia > maxStoreDistanceInKM) {
        print(
            '❌ [NEW_ORDER_CHECK] Pedido fora da distância máxima: $orderId, distância: $distancia km');
        return;
      }
      print('🔔 [NEW_ORDER_CHECK] Pedido aceitável: $orderId');
      _sendNewOrderNotification(orderData, vendor.vendor.title);
    } catch (e) {
      print("❌ [NEW_ORDER_CHECK] Erro ao verificar pedido $orderId: $e");
    }
  }

  void _sendNewOrderNotification(
      Map<String, dynamic> orderData, String vendorTitle) async {
    final orderId = orderData['id'] ?? 'ID_DESCONHECIDO';
    print('📢 [NOTIFICATION] Preparando notificação para pedido: $orderId');

    try {
      // Verificar se o pedido pode ser notificado (throttle de 5 minutos)
      final throttleService = NotificationThrottleService();
      if (!throttleService.canNotifyOrder(orderId)) {
        print(
            '⏰ [NOTIFICATION] Notificação bloqueada por throttle para pedido: $orderId');
        return;
      }

      print('📢 [NOTIFICATION] Loja: $vendorTitle');
      print('📢 [NOTIFICATION] Dados do pedido: $orderId');

      // Usar o NotificationService para criar a notificação apenas com ID
      final notificationService = NotificationService();
      await notificationService.showNewOrderNotification(
        orderId: orderId,
        vendorTitle: vendorTitle,
      );

      // Marcar o pedido como notificado
      throttleService.markOrderAsNotified(orderId);

      print('📢 [NOTIFICATION] Notificação exibida com sucesso!');
      print('📢 [NOTIFICATION] Enviando dados para UI principal...');

      FlutterForegroundTask.sendDataToMain({
        'action': 'new_order_available',
        'timestamp': DateTime.now().millisecondsSinceEpoch,
      });

      print('📢 [NOTIFICATION] Dados enviados para UI principal com sucesso!');
    } catch (e) {
      print(
          "❌ [NOTIFICATION] Erro ao enviar notificação para pedido $orderId: $e");
    }
  }

  /// Converte Timestamps para strings serializáveis recursivamente
  Map<String, dynamic> _convertTimestampsToStrings(Map<String, dynamic> data) {
    final Map<String, dynamic> converted = {};

    for (final entry in data.entries) {
      final key = entry.key;
      final value = entry.value;

      if (value is Timestamp) {
        // Converter Timestamp para ISO string
        converted[key] = value.toDate().toIso8601String();
      } else if (value is Map<String, dynamic>) {
        // Recursivamente converter mapas aninhados
        converted[key] = _convertTimestampsToStrings(value);
      } else if (value is List) {
        // Converter listas
        converted[key] = _convertTimestampsInList(value);
      } else {
        // Manter outros tipos como estão
        converted[key] = value;
      }
    }

    return converted;
  }

  /// Converte Timestamps em listas recursivamente
  List<dynamic> _convertTimestampsInList(List<dynamic> list) {
    return list.map((item) {
      if (item is Timestamp) {
        return item.toDate().toIso8601String();
      } else if (item is Map<String, dynamic>) {
        return _convertTimestampsToStrings(item);
      } else if (item is List) {
        return _convertTimestampsInList(item);
      } else {
        return item;
      }
    }).toList();
  }
}

class StatusEntregadorService {
  static final StatusEntregadorService _instancia =
      StatusEntregadorService._interno();

  final CollectionReference _colecaoStatus =
      FirebaseFirestore.instance.collection('delivery_men_status');

  final List<void Function(bool)> _listeners = [];
  final List<void Function(Map<String, dynamic>)> _dataListeners = [];

  bool _isOnline = false;
  bool _isServiceRunning = false;
  StreamSubscription<DocumentSnapshot>? _statusSubscription;

  // Instância do SignalR Service para comunicação em tempo real
  final SignalRService _signalRService = SignalRService();

  // Configurações do serviço em foreground
  static const int _serviceId = 256;
  static const String _channelId = 'status_entregador_channel';
  static const String _channelName = 'Status do Entregador';
  static const String _channelDescription =
      'Serviço para monitorar o status do entregador em background';

  factory StatusEntregadorService() {
    return _instancia;
  }

  StatusEntregadorService._interno() {
    _initializeForegroundTask();
  }

  // Getters
  bool get isOnline => _isOnline;
  bool get isServiceRunning => _isServiceRunning;

  // Inicialização do serviço em foreground
  void _initializeForegroundTask() {
    print('🔧 [SERVICE] Inicializando ForegroundTask...');

    // Inicializar porta de comunicação
    FlutterForegroundTask.initCommunicationPort();
    print('🔧 [SERVICE] Porta de comunicação inicializada');

    // Adicionar callback para receber dados do TaskHandler
    FlutterForegroundTask.addTaskDataCallback(_onReceiveTaskData);
    print('🔧 [SERVICE] Callback de dados adicionado');

    // Configurar o serviço
    FlutterForegroundTask.init(
      androidNotificationOptions: AndroidNotificationOptions(
        channelId: _channelId,
        channelName: _channelName,
        channelDescription: _channelDescription,
        onlyAlertOnce: true,
        channelImportance: NotificationChannelImportance.LOW,
        priority: NotificationPriority.LOW,
      ),
      iosNotificationOptions: const IOSNotificationOptions(
        showNotification: true,
        playSound: false,
      ),
      foregroundTaskOptions: ForegroundTaskOptions(
        eventAction: ForegroundTaskEventAction.repeat(10000), // 10 segundos
        autoRunOnBoot: false,
        autoRunOnMyPackageReplaced: true,
        allowWakeLock: true,
        allowWifiLock: true,
      ),
    );
  }

  // Callback para receber dados do TaskHandler
  void _onReceiveTaskData(Object data) {
    print('🔄 [SERVICE] _onReceiveTaskData chamado');
    print('🔄 [SERVICE] Tipo de dados: ${data.runtimeType}');

    if (data is Map<String, dynamic>) {
      final action = data['action'] as String?;
      print('🔄 [SERVICE] Action recebida: $action');
      print('🔄 [SERVICE] Dados completos: ${data.keys}');

      switch (action) {
        case 'status_updated':
          final isOnline = data['isOnline'] as bool? ?? false;
          _isOnline = isOnline;
          _notificarListeners(isOnline);
          break;
        case 'location_updated':
          // Notificar listeners de dados sobre atualização de localização
          _notificarDataListeners(data);
          break;
        case 'task_started':
          _isServiceRunning = true;
          print('🔄 [SERVICE] Task marcado como iniciado');
          break;
        case 'task_destroyed':
          _isServiceRunning = false;
          print('🔄 [SERVICE] Task marcado como parado');
          break;
        case 'stop_service_requested':
          // Parar o serviço quando solicitado pela notificação
          print(
              'StatusEntregadorService._onReceiveTaskData() - Parando serviço por solicitação do usuário');
          stopForegroundService();
          break;
        case 'notification_pressed':
          // Ação quando a notificação é pressionada
          _notificarDataListeners({'action': 'notification_pressed'});
          break;
        case 'orders_updated':
          print('🔄 [SERVICE] Dados de pedidos recebidos do TaskHandler!');
          _notificarDataListeners(data);
          break;
        case 'available_orders_updated':
          print('🔄 [SERVICE] Pedidos disponíveis atualizados!');
          _notificarDataListeners(data);
          break;
        case 'accepted_orders_updated':
          print('🔄 [SERVICE] Pedidos aceitos atualizados!');
          _notificarDataListeners(data);
          break;
        case 'return_orders_updated':
          print('🔄 [SERVICE] Pedidos de devolução atualizados!');
          _notificarDataListeners(data);
          break;
        case 'test_data':
          print('🧪 [SERVICE] Dados de teste recebidos do TaskHandler!');
          _notificarDataListeners(data);
          break;
      }

      // Notificar todos os listeners de dados
      print('🔄 [SERVICE] Notificando ${_dataListeners.length} listeners');
      _notificarDataListeners(data);
    } else {
      print('🔄 [SERVICE] Dados recebidos não são Map<String, dynamic>');
    }
  }

  // Inicialização do serviço
  Future<void> initialize() async {
    print('🔧 === INICIALIZANDO STATUS ENTREGADOR SERVICE ===');
    final String id = MyAppState.currentUser?.userID ?? '';
    print('👤 UserID obtido: $id');

    if (id.isEmpty) {
      print('❌ ERRO: UserID vazio, não é possível inicializar');
      print('   MyAppState.currentUser: ${MyAppState.currentUser}');
      return;
    }

    // Salvar ID do usuário para uso no TaskHandler
    print(
        'StatusEntregadorService.initialize() - Salvando UserID no TaskHandler');
    await FlutterForegroundTask.saveData(key: 'user_id', value: id);

    // Cancelar subscription anterior se existir
    print(
        'StatusEntregadorService.initialize() - Cancelando subscription anterior');
    _statusSubscription?.cancel();

    // Obter status salvo localmente primeiro
    print(
        'StatusEntregadorService.initialize() - Verificando status salvo localmente');
    final statusSalvoLocalmente = UserPreference.getOnlineStatus();
    print(
        'StatusEntregadorService.initialize() - Status salvo localmente: $statusSalvoLocalmente');

    // Obter status do Firebase
    print('StatusEntregadorService.initialize() - Obtendo status do Firebase');
    final doc = await _colecaoStatus.doc(id).get();
    final dados = doc.data() as Map<String, dynamic>?;
    final statusFirebase = dados?['isOnline'] == true;
    print(
        'StatusEntregadorService.initialize() - Status no Firebase: $statusFirebase');

    // Usar o status salvo localmente se existir, senão usar o do Firebase
    _isOnline = statusSalvoLocalmente;
    print(
        'StatusEntregadorService.initialize() - Status inicial definido: $_isOnline');

    // Inicializar SignalR Service
    try {
      print('🔧 [SERVICE] Inicializando SignalR Service...');
      await _signalRService.initialize();
      await _signalRService.connect();

      // Configurar callback para mudanças de status
      _signalRService.onConnectionStateChanged = (isConnected) {
        print('🔗 [SERVICE] SignalR conexão: $isConnected');
      };

      // Atualizar status inicial no SignalR
      _signalRService.updateOnlineStatus(_isOnline);
      print('✅ [SERVICE] SignalR Service inicializado com sucesso');
    } catch (e) {
      print('❌ [SERVICE] Erro ao inicializar SignalR: $e');
    }

    // Se o status local for diferente do Firebase, atualizar o Firebase
    if (statusSalvoLocalmente != statusFirebase) {
      print(
          'StatusEntregadorService.initialize() - Sincronizando status local com Firebase');
      await _atualizarStatusNoFirebase(_isOnline);
    }

    // Configurar listener do Firestore
    print(
        'StatusEntregadorService.initialize() - Configurando listener do Firestore');
    _statusSubscription = _colecaoStatus.doc(id).snapshots().listen((doc) {
      final dados = doc.data() as Map<String, dynamic>?;
      final online = dados?['isOnline'] == true;
      print(
          'StatusEntregadorService.initialize() - Listener: Status mudou para $online');

      // Só atualizar se realmente mudou e não foi uma mudança local
      if (_isOnline != online) {
        _isOnline = online;
        // Salvar também no UserPreference para persistir
        UserPreference.setOnlineStatus(online);
        _notificarListeners(online);
      }
    });

    // Salvar status inicial no TaskHandler e UserPreference
    print(
        'StatusEntregadorService.initialize() - Salvando status inicial no TaskHandler e UserPreference');
    await FlutterForegroundTask.saveData(key: 'is_online', value: _isOnline);
    await UserPreference.setOnlineStatus(_isOnline);
    print('StatusEntregadorService.initialize() - Concluído com sucesso');
  }

  /// Atualiza o status no Firebase sem notificar listeners
  Future<void> _atualizarStatusNoFirebase(bool online) async {
    final String id = MyAppState.currentUser?.userID ?? '';

    if (id.isEmpty) {
      return;
    }

    try {
      final Map<String, dynamic> dados = {
        'entregador_id': id,
        'lastActive': Timestamp.now(),
        'isOnline': online,
        'updatedAt': Timestamp.now(),
      };

      await _colecaoStatus.doc(id).set(dados, SetOptions(merge: true));
      print(
          'StatusEntregadorService._atualizarStatusNoFirebase() - Status atualizado no Firebase: $online');
    } catch (e) {
      print('StatusEntregadorService._atualizarStatusNoFirebase() - Erro: $e');
    }
  }

  // Solicitar permissões necessárias
  Future<bool> requestPermissions() async {
    print('StatusEntregadorService.requestPermissions() - Iniciando...');
    try {
      // Verificar permissão de notificação
      print(
          'StatusEntregadorService.requestPermissions() - Verificando permissão de notificação');
      final notificationPermission =
          await FlutterForegroundTask.checkNotificationPermission();
      print(
          'StatusEntregadorService.requestPermissions() - Permissão de notificação: $notificationPermission');

      if (notificationPermission != NotificationPermission.granted) {
        print(
            'StatusEntregadorService.requestPermissions() - Solicitando permissão de notificação');
        await FlutterForegroundTask.requestNotificationPermission();
      }

      if (Platform.isAndroid) {
        // Verificar otimização de bateria
        print(
            'StatusEntregadorService.requestPermissions() - Verificando otimização de bateria');
        if (!await FlutterForegroundTask.isIgnoringBatteryOptimizations) {
          print(
              'StatusEntregadorService.requestPermissions() - Solicitando ignorar otimização de bateria');
          await FlutterForegroundTask.requestIgnoreBatteryOptimization();
        }
      }

      // Verificar permissão de localização
      print(
          'StatusEntregadorService.requestPermissions() - Verificando permissão de localização');
      LocationPermission permission = await Geolocator.checkPermission();
      print(
          'StatusEntregadorService.requestPermissions() - Permissão de localização atual: $permission');

      if (permission == LocationPermission.denied) {
        print(
            'StatusEntregadorService.requestPermissions() - Solicitando permissão de localização');
        permission = await Geolocator.requestPermission();
        print(
            'StatusEntregadorService.requestPermissions() - Nova permissão de localização: $permission');
      }

      final result = permission != LocationPermission.denied &&
          permission != LocationPermission.deniedForever;
      print(
          'StatusEntregadorService.requestPermissions() - Resultado final: $result');
      return result;
    } catch (e) {
      print('StatusEntregadorService.requestPermissions() - Erro: $e');
      return false;
    }
  }

  // Iniciar serviço em foreground
  Future<bool> startForegroundService() async {
    print('StatusEntregadorService.startForegroundService() - Iniciando...');
    try {
      print(
          'StatusEntregadorService.startForegroundService() - Verificando se serviço já está rodando');
      if (await FlutterForegroundTask.isRunningService) {
        print(
            'StatusEntregadorService.startForegroundService() - Serviço já está rodando');
        return true;
      }

      print(
          'StatusEntregadorService.startForegroundService() - Iniciando serviço foreground');
      final result = await FlutterForegroundTask.startService(
        serviceId: _serviceId,
        notificationTitle: 'Taliso - Status do Entregador',
        notificationText: 'Monitorando status em background',
        notificationButtons: [
          const NotificationButton(
            id: 'btn_toggle_status',
            text: 'Parar Processo',
          ),
          const NotificationButton(
            id: 'btn_update_location',
            text: 'Atualizar',
          ),
        ],
        callback: startStatusCallback,
      );

      print(
          'StatusEntregadorService.startForegroundService() - Resultado: $result');
      _isServiceRunning = result is ServiceRequestSuccess;
      print(
          'StatusEntregadorService.startForegroundService() - Serviço rodando: $_isServiceRunning');
      return _isServiceRunning;
    } catch (e) {
      print('StatusEntregadorService.startForegroundService() - Erro: $e');
      return false;
    }
  }

  // Parar serviço em foreground
  Future<bool> stopForegroundService() async {
    try {
      final result = await FlutterForegroundTask.stopService();
      _isServiceRunning = false;

      return result is ServiceRequestSuccess;
    } catch (e) {
      return false;
    }
  }

  // Definir status online/offline
  Future<void> setOnlineStatus(bool online,
      {double? latitude, double? longitude}) async {
    final String id = MyAppState.currentUser?.userID ?? '';

    if (id.isEmpty) {
      return;
    }

    try {
      final Map<String, dynamic> dados = {
        'entregador_id': id,
        'lastActive': Timestamp.now(),
        'isOnline': online,
        'updatedAt': Timestamp.now(),
      };

      if (latitude != null && longitude != null) {
        dados['location'] = {
          'latitude': latitude,
          'longitude': longitude,
        };
      }

      await _colecaoStatus.doc(id).set(dados, SetOptions(merge: true));

      _isOnline = online;
      _notificarListeners(online);

      // Atualizar status no SignalR
      _signalRService.updateOnlineStatus(online);
      print('🔗 [SERVICE] Status SignalR atualizado para: $online');

      // Salvar no armazenamento local do TaskHandler e UserPreference
      await FlutterForegroundTask.saveData(key: 'is_online', value: online);
      await UserPreference.setOnlineStatus(online);

      if (online && !_isServiceRunning) {
        final serviceStarted = await startForegroundService();

        if (serviceStarted) {
          print('✅ SERVIÇO FOREGROUND INICIADO COM SUCESSO');
        } else {
          print('❌ FALHA AO INICIAR SERVIÇO FOREGROUND');
        }
      } else if (!online && _isServiceRunning) {
        print('🛑 USUÁRIO FICOU OFFLINE - PARANDO SERVIÇO FOREGROUND');
        await stopForegroundService();
      } else if (online && _isServiceRunning) {
        print('ℹ️ USUÁRIO JÁ ONLINE E SERVIÇO JÁ RODANDO');
      } else {
        print('ℹ️ USUÁRIO OFFLINE E SERVIÇO PARADO');
      }

      if (_isServiceRunning) {
        FlutterForegroundTask.sendDataToTask({
          'action': 'update_status',
          'isOnline': online,
        });
      } else {}
    } catch (e) {}
  }

  // Forçar atualização de localização
  Future<void> updateLocation() async {
    if (_isServiceRunning) {
      FlutterForegroundTask.sendDataToTask({
        'action': 'update_location',
      });
    }
  }

  // Forçar atualização de pedidos (para teste)
  Future<void> forceOrdersUpdate() async {
    print('🧪 [SERVICE] Forçando atualização de pedidos...');
    if (_isServiceRunning) {
      FlutterForegroundTask.sendDataToTask({
        'action': 'force_orders_update',
      });
      print('🧪 [SERVICE] Comando enviado para TaskHandler');
    } else {
      print('🧪 [SERVICE] Serviço não está rodando!');
    }
  }

  // Forçar atualização completa (status + localização)
  Future<void> forceUpdate() async {
    if (_isServiceRunning) {
      FlutterForegroundTask.sendDataToTask({
        'action': 'force_update',
      });
    }
  }

  // Obter status do entregador
  Future<Map<String, dynamic>?> obterStatus({String? idEntregador}) async {
    final String id = idEntregador ?? MyAppState.currentUser?.userID ?? '';
    if (id.isEmpty) {
      throw Exception('ID do entregador não disponível');
    }

    try {
      final doc = await _colecaoStatus.doc(id).get();
      return doc.data() as Map<String, dynamic>?;
    } catch (e) {
      return null;
    }
  }

  // Adicionar listener para mudanças de status
  void addListener(void Function(bool) listener) {
    _listeners.add(listener);
  }

  // Remover listener de status
  void removeListener(void Function(bool) listener) {
    _listeners.remove(listener);
  }

  // Adicionar listener para dados do TaskHandler
  void addDataListener(void Function(Map<String, dynamic>) listener) {
    print(
        '📝 [SERVICE] Adicionando data listener. Total: ${_dataListeners.length + 1}');
    _dataListeners.add(listener);
    print('📝 [SERVICE] Data listener adicionado com sucesso');
  }

  // Remover listener de dados
  void removeDataListener(void Function(Map<String, dynamic>) listener) {
    _dataListeners.remove(listener);
  }

  // Verificar se o serviço está rodando
  Future<bool> isServiceActive() async {
    return await FlutterForegroundTask.isRunningService;
  }

  // Obter dados salvos localmente
  Future<T?> getSavedData<T>(String key) async {
    try {
      return await FlutterForegroundTask.getData(key: key) as T?;
    } catch (e) {
      return null;
    }
  }

  // Salvar dados localmente
  Future<void> saveData(String key, Object value) async {
    try {
      await FlutterForegroundTask.saveData(key: key, value: value);
    } catch (e) {}
  }

  // Limpar todos os dados salvos
  Future<void> clearAllData() async {
    try {
      await FlutterForegroundTask.clearAllData();
    } catch (e) {}
  }

  // Limpar dados de status (usado no logout)
  Future<void> clearStatusData() async {
    print(
        'StatusEntregadorService.clearStatusData() - Limpando dados de status');

    // Limpar status local
    _isOnline = false;

    // Limpar UserPreference
    await UserPreference.clearOnlineStatus();

    // Limpar dados do TaskHandler
    await FlutterForegroundTask.saveData(key: 'is_online', value: false);
    await FlutterForegroundTask.saveData(key: 'user_id', value: '');

    // Cancelar subscription
    _statusSubscription?.cancel();

    print(
        'StatusEntregadorService.clearStatusData() - Dados limpos com sucesso');
  }

  // Dispose do serviço
  void dispose() {
    _statusSubscription?.cancel();
    _listeners.clear();
    _dataListeners.clear();
    FlutterForegroundTask.removeTaskDataCallback(_onReceiveTaskData);

    // Dispose do SignalR Service
    _signalRService.dispose();
    print('🧹 [SERVICE] SignalR Service disposed');
  }

  // Métodos privados para notificação de listeners
  void _notificarListeners(bool online) {
    for (final listener in _listeners) {
      try {
        listener(online);
      } catch (e) {}
    }
  }

  void _notificarDataListeners(Map<String, dynamic> data) {
    print('🔔 [DATA_LISTENERS] Notificando ${_dataListeners.length} listeners');
    print('🔔 [DATA_LISTENERS] Dados: ${data.keys}');

    for (final listener in _dataListeners) {
      try {
        print('🔔 [DATA_LISTENERS] Chamando listener...');
        listener(data);
        print('🔔 [DATA_LISTENERS] Listener chamado com sucesso');
      } catch (e) {
        print('❌ [DATA_LISTENERS] Erro ao chamar listener: $e');
      }
    }
  }

  // Métodos utilitários para configuração avançada

  // Atualizar configurações do serviço
  Future<void> updateServiceConfiguration({
    int? intervalSeconds,
    bool? autoRunOnBoot,
    bool? allowWakeLock,
    bool? allowWifiLock,
  }) async {
    try {
      if (_isServiceRunning) {
        await FlutterForegroundTask.updateService(
          foregroundTaskOptions: ForegroundTaskOptions(
            eventAction: ForegroundTaskEventAction.repeat(
              (intervalSeconds ?? 10) * 1000,
            ),
            autoRunOnBoot: autoRunOnBoot ?? false,
            autoRunOnMyPackageReplaced: true,
            allowWakeLock: allowWakeLock ?? true,
            allowWifiLock: allowWifiLock ?? true,
          ),
        );
      }
    } catch (e) {}
  }

  // Atualizar texto da notificação
  Future<void> updateNotification({
    String? title,
    String? text,
    List<NotificationButton>? buttons,
  }) async {
    try {
      if (_isServiceRunning) {
        await FlutterForegroundTask.updateService(
          notificationTitle: title,
          notificationText: text,
          notificationButtons: buttons,
        );
      }
    } catch (e) {}
  }

  // Obter estatísticas do serviço
  Future<Map<String, dynamic>> getServiceStats() async {
    try {
      final allData = await FlutterForegroundTask.getAllData();
      final isRunning = await FlutterForegroundTask.isRunningService;

      return {
        'isServiceRunning': isRunning,
        'isOnline': _isOnline,
        'listenersCount': _listeners.length,
        'dataListenersCount': _dataListeners.length,
        'savedData': allData,
        'lastUpdate': DateTime.now().toIso8601String(),
      };
    } catch (e) {
      return {
        'error': e.toString(),
        'isServiceRunning': false,
        'isOnline': _isOnline,
      };
    }
  }
}
