import 'dart:developer';

import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:emartdriver/model/OrderModel.dart';
import 'package:emartdriver/services/location_service.dart';
import 'package:emartdriver/services/notification_service.dart';
import 'package:emartdriver/services/order_service.dart';
import 'package:flutter/material.dart';

/// Service que gerencia a integração entre notificações e a HomeScreen
/// Responsável por abrir o bottomsheet quando uma notificação é clicada
class NotificationHandlerService {
  static final NotificationHandlerService _instance =
      NotificationHandlerService._internal();
  factory NotificationHandlerService() => _instance;
  NotificationHandlerService._internal();

  // Callback para mostrar o bottomsheet
  Function(OrderModel)? onShowOrderBottomSheet;

  // Referência aos services necessários
  final OrderService _orderService = OrderService();
  final LocationService _locationService = LocationService();

  /// Inicializa o handler de notificações
  void initialize() {
    // Configurar callback para cliques em notificações
    NotificationService.onOrderNotificationTap = _handleNotificationTap;
    log('✅ [NOTIFICATION_HANDLER] Handler inicializado');
  }

  /// Trata o clique em uma notificação de pedido
  void _handleNotificationTap(Map<String, dynamic> payloadData) {
    try {
      // Verificar se é uma notificação de novo pedido
      if (payloadData['type'] != 'new_order' ||
          payloadData['orderId'] == null) {
        return;
      }

      final orderId = payloadData['orderId'] as String;
      log('📱 [PEDIDO] Abrindo pedido: $orderId');

      // Buscar o pedido no Firebase pelo ID
      _fetchOrderById(orderId);
    } catch (e) {
      log('❌ [NOTIFICATION_HANDLER] Erro ao processar clique: $e');
    }
  }

  /// Busca um pedido específico no Firebase pelo ID
  Future<void> _fetchOrderById(String orderId) async {
    try {
      final doc = await FirebaseFirestore.instance
          .collection('vendor_orders')
          .doc(orderId)
          .get();

      if (!doc.exists) {
        log('❌ [PEDIDO] Pedido não encontrado: $orderId');
        return;
      }

      final orderData = doc.data()!;
      final order = OrderModel.fromJson(orderData);

      log('✅ [PEDIDO] ${order.vendor.title} - ${order.id.substring(0, 8)}');

      // Verificar se temos posição atual
      if (_locationService.currentPosition == null) {
        await _locationService.determinePosition();
        if (_locationService.currentPosition == null) {
          log('❌ [PEDIDO] Posição não disponível');
          return;
        }
      }

      // Selecionar o pedido no OrderService
      _orderService.selectOrderAndCalculateRoute(
          order, _locationService.currentPosition!);

      // Chamar callback para mostrar bottomsheet
      if (onShowOrderBottomSheet != null) {
        onShowOrderBottomSheet!(order);
      }
    } catch (e) {
      log('❌ [PEDIDO] Erro ao buscar: $e');
    }
  }

  /// Define o callback para mostrar o bottomsheet
  void setBottomSheetCallback(Function(OrderModel) callback) {
    onShowOrderBottomSheet = callback;
  }

  /// Remove o callback
  void removeBottomSheetCallback() {
    onShowOrderBottomSheet = null;
  }

  /// Limpa os recursos
  void dispose() {
    NotificationService.onOrderNotificationTap = null;
    onShowOrderBottomSheet = null;
  }
}

/// Exemplo de como integrar com a HomeScreen
class NotificationIntegrationExample {
  final NotificationHandlerService _notificationHandler =
      NotificationHandlerService();

  void setupNotificationIntegration(BuildContext context) {
    // Inicializar o handler
    _notificationHandler.initialize();

    // Definir callback para mostrar bottomsheet
    _notificationHandler.setBottomSheetCallback((order) {
      _showOrderBottomSheetFromNotification(context, order);
    });
  }

  void _showOrderBottomSheetFromNotification(
      BuildContext context, OrderModel order) {
    log('📱 [INTEGRATION] Mostrando bottomsheet para pedido: ${order.id}');

    // Navegar para a HomeScreen se necessário
    _navigateToHomeScreen(context);

    // Mostrar bottomsheet após um pequeno delay para garantir que a tela carregou
    Future.delayed(const Duration(milliseconds: 500), () {
      if (context.mounted) {
        _showBottomSheet(context, order);
      }
    });
  }

  void _navigateToHomeScreen(BuildContext context) {
    // Implementar navegação para HomeScreen se necessário
    // Navigator.pushNamedAndRemoveUntil(context, '/home', (route) => false);
  }

  void _showBottomSheet(BuildContext context, OrderModel order) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.white,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      builder: (BuildContext context) {
        return Container(
          height: MediaQuery.of(context).size.height * 0.6,
          padding: const EdgeInsets.all(16),
          child: Column(
            children: [
              // Handle bar
              Container(
                width: 50,
                height: 5,
                decoration: BoxDecoration(
                  color: Colors.grey[300],
                  borderRadius: BorderRadius.circular(10),
                ),
              ),
              const SizedBox(height: 20),

              // Título
              const Text(
                'Novo Pedido Disponível!',
                style: TextStyle(
                  fontSize: 20,
                  fontWeight: FontWeight.bold,
                  color: Color(0xff425799),
                ),
              ),
              const SizedBox(height: 16),

              // Informações da loja
              Container(
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: Colors.grey[100],
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Loja: ${order.vendor.title}',
                      style: const TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 8),
                    Text(
                      'Pedido: ${order.id}',
                      style: const TextStyle(fontSize: 14),
                    ),
                  ],
                ),
              ),

              const Spacer(),

              // Botões
              Row(
                children: [
                  Expanded(
                    child: ElevatedButton(
                      onPressed: () {
                        Navigator.pop(context);
                        // Implementar lógica de recusar
                      },
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Colors.grey[400],
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(12),
                        ),
                      ),
                      child: const Text(
                        'Recusar',
                        style: TextStyle(color: Colors.white),
                      ),
                    ),
                  ),
                  const SizedBox(width: 16),
                  Expanded(
                    child: ElevatedButton(
                      onPressed: () {
                        Navigator.pop(context);
                        // Implementar lógica de aceitar
                        _acceptOrder(order);
                      },
                      style: ElevatedButton.styleFrom(
                        backgroundColor: const Color(0xff425799),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(12),
                        ),
                      ),
                      child: const Text(
                        'Aceitar',
                        style: TextStyle(color: Colors.white),
                      ),
                    ),
                  ),
                ],
              ),
            ],
          ),
        );
      },
    );
  }

  void _acceptOrder(OrderModel order) async {
    log('✅ [INTEGRATION] Aceitando pedido: ${order.id}');

    try {
      // Criar uma instância do OrderService
      final orderService = OrderService();

      // Primeiro, selecionar o pedido no OrderService
      orderService.selectOrder(order);

      // Usar o OrderService para aceitar o pedido com validação
      final result = await orderService.acceptOrderWithValidation();

      if (result.success) {
        log('✅ [INTEGRATION] Pedido aceito com sucesso: ${order.id}');
        // Aqui você pode adicionar lógica para mostrar sucesso ou navegar para outra tela
      } else {
        log('❌ [INTEGRATION] Falha ao aceitar pedido: ${result.message}');
        // Aqui você pode mostrar um dialog de erro se tiver acesso ao context
        // Por exemplo, através de um callback para a UI
      }
    } catch (e) {
      log('❌ [INTEGRATION] Erro ao aceitar pedido: $e');
    }
  }

  void dispose() {
    _notificationHandler.dispose();
  }
}
