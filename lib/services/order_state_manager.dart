import 'dart:developer';

import 'package:emartdriver/model/OrderModel.dart';

/// Service para gerenciar o estado dos pedidos e detectar mudanças
/// Responsável por identificar pedidos que devem ser removidos da tela
class OrderStateManager {
  static final OrderStateManager _instance = OrderStateManager._internal();
  factory OrderStateManager() => _instance;
  OrderStateManager._internal();

  // Mapa para armazenar pedidos atualmente exibidos
  final Map<String, OrderModel> _currentOrders = {};
  final Map<String, DateTime> _orderLastSeen = {};

  // Configurações
  static const Duration _orderTimeout = Duration(minutes: 5);

  /// Atualiza a lista de pedidos e retorna quais devem ser removidos
  List<String> updateOrders(List<OrderModel> newOrders) {
    final currentTime = DateTime.now();
    final newOrderIds = newOrders.map((order) => order.id).toSet();
    final removedOrderIds = <String>[];

    // Atualizar pedidos existentes e adicionar novos
    for (final order in newOrders) {
      _currentOrders[order.id] = order;
      _orderLastSeen[order.id] = currentTime;
    }

    // Identificar pedidos que não estão mais na lista
    final currentOrderIds = _currentOrders.keys.toSet();
    final missingOrderIds = currentOrderIds.difference(newOrderIds);

    for (final orderId in missingOrderIds) {
      final lastSeen = _orderLastSeen[orderId];
      if (lastSeen != null) {
        final timeSinceLastSeen = currentTime.difference(lastSeen);

        if (timeSinceLastSeen > _orderTimeout) {
          // Pedido não visto há muito tempo, remover
          removedOrderIds.add(orderId);
          _currentOrders.remove(orderId);
          _orderLastSeen.remove(orderId);
        }
      } else {
        // Pedido sem registro de última visualização, remover
        removedOrderIds.add(orderId);
        _currentOrders.remove(orderId);
      }
    }

    return removedOrderIds;
  }

  /// Marca um pedido como aceito (não deve ser removido automaticamente)
  void markOrderAsAccepted(String orderId) {
    log("✅ [ORDER_STATE] Pedido $orderId marcado como aceito");
    // Pedidos aceitos não são removidos automaticamente
    _orderLastSeen[orderId] = DateTime.now().add(const Duration(days: 1));
  }

  /// Remove um pedido específico do estado
  void removeOrder(String orderId) {
    log("🗑️ [ORDER_STATE] Removendo pedido $orderId do estado");
    _currentOrders.remove(orderId);
    _orderLastSeen.remove(orderId);
  }

  /// Verifica se um pedido existe no estado atual
  bool hasOrder(String orderId) {
    return _currentOrders.containsKey(orderId);
  }

  /// Obtém um pedido pelo ID
  OrderModel? getOrder(String orderId) {
    return _currentOrders[orderId];
  }

  /// Obtém todos os pedidos atuais
  List<OrderModel> getAllOrders() {
    return _currentOrders.values.toList();
  }

  /// Obtém estatísticas do estado atual
  Map<String, dynamic> getStats() {
    final currentTime = DateTime.now();
    final recentOrders = _orderLastSeen.values
        .where((lastSeen) => currentTime.difference(lastSeen).inMinutes < 5)
        .length;

    return {
      'totalOrders': _currentOrders.length,
      'recentOrders': recentOrders,
      'oldestOrderAge': _getOldestOrderAge(),
      'newestOrderAge': _getNewestOrderAge(),
    };
  }

  Duration? _getOldestOrderAge() {
    if (_orderLastSeen.isEmpty) return null;

    final currentTime = DateTime.now();
    final oldestTime =
        _orderLastSeen.values.reduce((a, b) => a.isBefore(b) ? a : b);
    return currentTime.difference(oldestTime);
  }

  Duration? _getNewestOrderAge() {
    if (_orderLastSeen.isEmpty) return null;

    final currentTime = DateTime.now();
    final newestTime =
        _orderLastSeen.values.reduce((a, b) => a.isAfter(b) ? a : b);
    return currentTime.difference(newestTime);
  }

  /// Limpa pedidos expirados manualmente
  List<String> cleanupExpiredOrders() {
    final currentTime = DateTime.now();
    final expiredOrderIds = <String>[];

    for (final entry in _orderLastSeen.entries) {
      final orderId = entry.key;
      final lastSeen = entry.value;
      final timeSinceLastSeen = currentTime.difference(lastSeen);

      if (timeSinceLastSeen > _orderTimeout) {
        expiredOrderIds.add(orderId);
      }
    }

    for (final orderId in expiredOrderIds) {
      _currentOrders.remove(orderId);
      _orderLastSeen.remove(orderId);
    }

    if (expiredOrderIds.isNotEmpty) {
      log("🧹 [ORDER_STATE] Limpeza manual: ${expiredOrderIds.length} pedidos expirados removidos");
    }

    return expiredOrderIds;
  }

  /// Limpa todos os pedidos do estado
  void clearAllOrders() {
    log("🗑️ [ORDER_STATE] Limpando todos os pedidos do estado");
    _currentOrders.clear();
    _orderLastSeen.clear();
  }

  /// Força a atualização do timestamp de um pedido
  void touchOrder(String orderId) {
    if (_currentOrders.containsKey(orderId)) {
      _orderLastSeen[orderId] = DateTime.now();
      log("👆 [ORDER_STATE] Timestamp do pedido $orderId atualizado");
    }
  }
}

/// Exemplo de uso do OrderStateManager
class OrderStateExample {
  final OrderStateManager _stateManager = OrderStateManager();

  void handleNewOrders(List<OrderModel> newOrders) {
    // Atualizar estado e obter pedidos a remover
    final ordersToRemove = _stateManager.updateOrders(newOrders);

    // Remover marcadores dos pedidos que não existem mais
    for (final orderId in ordersToRemove) {
      log("🗑️ Removendo marcador do pedido: $orderId");
      // mapService.removeMarker('loja_$orderId');
    }

    // Adicionar marcadores dos novos pedidos
    for (final order in newOrders) {
      log("📍 Adicionando marcador do pedido: ${order.id}");
      // mapService.addMarker('loja_${order.vendor.id}', marker);
    }
  }

  void handleOrderAccepted(String orderId) {
    _stateManager.markOrderAsAccepted(orderId);
  }

  void handleOrderDeclined(String orderId) {
    _stateManager.removeOrder(orderId);
  }

  void performCleanup() {
    final expiredOrders = _stateManager.cleanupExpiredOrders();

    for (final orderId in expiredOrders) {
      log("🧹 Removendo marcador do pedido expirado: $orderId");
      // mapService.removeMarker('loja_$orderId');
    }
  }

  void getStateInfo() {
    final stats = _stateManager.getStats();
    log("📊 Estado dos pedidos: $stats");
  }
}
