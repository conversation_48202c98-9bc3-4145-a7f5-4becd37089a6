import 'dart:convert';
import 'dart:developer';

import 'package:emartdriver/main.dart';
import 'package:http/http.dart' as http;

class DeliveryApiService {
  static const String baseUrl =
      'https://desenv.taentregue.site/mobile/v1/lojista/pedido';

  /// Confirma a corrida/entrega do pedido na API externa
  static Future<bool> confirmarCorrida({
    required String idPedido,
  }) async {
    try {
      log('🚚 [DELIVERY_API] Confirmando corrida para pedido: $idPedido');

      final url = Uri.parse('$baseUrl/creditar-saldo-entregador.php');

      final body = {
        "id_pedido": idPedido,
        "id_entregador": MyAppState.currentUser?.userID ?? '',
        "status": "ORDER_STATUS_DELIVERED",
      };

      final response = await http.post(
        url,
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json',
        },
        body: jsonEncode(body),
      );

      if (response.statusCode == 200) {
        return true;
      } else {
        log('❌ [DELIVERY_API] Erro HTTP: ${response.statusCode}');
        log('❌ [DELIVERY_API] Response: ${response.body}');
        return true;
      }
    } catch (e, stackTrace) {
      log('❌ [DELIVERY_API] Erro na chamada da API: $e');
      log('❌ [DELIVERY_API] Stack trace: $stackTrace');
      return true;
    }
  }

  /// Confirma a entrega do pedido (wrapper para confirmarCorrida)
  static Future<bool> confirmarEntrega({
    required String idPedido,
  }) async {
    final idEntregador = MyAppState.currentUser?.userID ?? '';
    return await confirmarCorrida(
      idPedido: idPedido,
    );
  }
}
