import 'dart:async';
import 'dart:developer';
import 'dart:ui';

import 'package:emartdriver/services/location_service.dart';
import 'package:emartdriver/services/order_service.dart';

/// Service responsável por gerenciar atualizações periódicas da tela
/// Atualiza pedidos a cada 5 segundos quando o usuário está online
class PeriodicUpdateService {
  static final PeriodicUpdateService _instance =
      PeriodicUpdateService._internal();
  factory PeriodicUpdateService() => _instance;
  PeriodicUpdateService._internal();

  Timer? _updateTimer;
  bool _isRunning = false;
  bool _isOnline = false;

  // Configurações
  static const Duration _defaultInterval = Duration(seconds: 5);
  Duration _currentInterval = _defaultInterval;

  // Services
  final OrderService _orderService = OrderService();
  final LocationService _locationService = LocationService();

  // Callbacks
  VoidCallback? onUpdateStarted;
  VoidCallback? onUpdateCompleted;
  Function(int)? onUpdateTick;

  bool get isRunning => _isRunning;
  Duration get currentInterval => _currentInterval;

  /// Inicia as atualizações periódicas
  void startPeriodicUpdates({
    bool isOnline = true,
    Duration? customInterval,
  }) {
    if (_isRunning) {
      log('⚠️ [PERIODIC_UPDATE] Atualizações já estão rodando');
      return;
    }

    _isOnline = isOnline;
    _currentInterval = customInterval ?? _defaultInterval;

    if (!_isOnline) {
      log('📴 [PERIODIC_UPDATE] Usuário offline - não iniciando atualizações');
      return;
    }

    log('🔄 [PERIODIC_UPDATE] Iniciando atualizações periódicas...');
    log('⏰ [PERIODIC_UPDATE] Intervalo: ${_currentInterval.inSeconds} segundos');

    _isRunning = true;
    onUpdateStarted?.call();

    // Primeira atualização imediata
    _performUpdate(0);

    // Timer periódico
    _updateTimer = Timer.periodic(_currentInterval, (timer) {
      _performUpdate(timer.tick);
    });

    log('✅ [PERIODIC_UPDATE] Atualizações periódicas iniciadas');
  }

  /// Para as atualizações periódicas
  void stopPeriodicUpdates() {
    if (!_isRunning) {
      log('⚠️ [PERIODIC_UPDATE] Atualizações já estão paradas');
      return;
    }

    log('🛑 [PERIODIC_UPDATE] Parando atualizações periódicas...');

    _updateTimer?.cancel();
    _updateTimer = null;
    _isRunning = false;

    log('✅ [PERIODIC_UPDATE] Atualizações periódicas paradas');
  }

  /// Atualiza o status online/offline
  void updateOnlineStatus(bool isOnline) {
    final wasOnline = _isOnline;
    _isOnline = isOnline;

    log('📶 [PERIODIC_UPDATE] Status online alterado: $wasOnline -> $isOnline');

    if (!isOnline && _isRunning) {
      log('📴 [PERIODIC_UPDATE] Usuário offline - parando atualizações');
      stopPeriodicUpdates();
    } else if (isOnline && !_isRunning && wasOnline != isOnline) {
      log('📶 [PERIODIC_UPDATE] Usuário online - iniciando atualizações');
      startPeriodicUpdates(isOnline: isOnline);
    }
  }

  /// Altera o intervalo de atualização
  void changeInterval(Duration newInterval) {
    log('⏰ [PERIODIC_UPDATE] Alterando intervalo: ${_currentInterval.inSeconds}s -> ${newInterval.inSeconds}s');

    _currentInterval = newInterval;

    if (_isRunning) {
      // Reiniciar com novo intervalo
      stopPeriodicUpdates();
      startPeriodicUpdates(isOnline: _isOnline, customInterval: newInterval);
    }
  }

  /// Força uma atualização imediata
  void forceUpdate() {
    log('🔄 [PERIODIC_UPDATE] Forçando atualização imediata...');
    _performUpdate(-1); // -1 indica atualização forçada
  }

  /// Executa uma atualização
  void _performUpdate(int tick) {
    try {
      if (tick == -1) {
        log('🔄 [PERIODIC_UPDATE] Executando atualização forçada');
      } else if (tick == 0) {
        log('🔄 [PERIODIC_UPDATE] Executando primeira atualização');
      } else {
        log('⏰ [PERIODIC_UPDATE] Atualização automática #$tick');
      }

      onUpdateTick?.call(tick);

      // Verificar se temos posição atual
      final currentPosition = _locationService.currentPosition;
      if (currentPosition == null) {
        log('⚠️ [PERIODIC_UPDATE] Posição atual não disponível');
        return;
      }

      // Atualizar pedidos
      _orderService.fetchOrders(currentPosition, _isOnline);

      onUpdateCompleted?.call();

      if (tick >= 0) {
        log('✅ [PERIODIC_UPDATE] Atualização #$tick concluída');
      } else {
        log('✅ [PERIODIC_UPDATE] Atualização forçada concluída');
      }
    } catch (e) {
      log('❌ [PERIODIC_UPDATE] Erro na atualização: $e');
    }
  }

  /// Retorna estatísticas das atualizações
  Map<String, dynamic> getStats() {
    return {
      'isRunning': _isRunning,
      'isOnline': _isOnline,
      'intervalSeconds': _currentInterval.inSeconds,
      'hasTimer': _updateTimer != null,
      'hasPosition': _locationService.currentPosition != null,
    };
  }

  /// Configura callbacks personalizados
  void setCallbacks({
    VoidCallback? onStarted,
    VoidCallback? onCompleted,
    Function(int)? onTick,
  }) {
    onUpdateStarted = onStarted;
    onUpdateCompleted = onCompleted;
    onUpdateTick = onTick;
  }

  /// Limpa os recursos
  void dispose() {
    log('🗑️ [PERIODIC_UPDATE] Limpando recursos...');

    stopPeriodicUpdates();

    onUpdateStarted = null;
    onUpdateCompleted = null;
    onUpdateTick = null;

    log('✅ [PERIODIC_UPDATE] Recursos limpos');
  }
}

/// Exemplo de uso do PeriodicUpdateService
class PeriodicUpdateExample {
  final PeriodicUpdateService _periodicService = PeriodicUpdateService();

  void setupPeriodicUpdates() {
    // Configurar callbacks
    _periodicService.setCallbacks(
      onStarted: () => log('🚀 Atualizações iniciadas'),
      onCompleted: () => log('✅ Atualização concluída'),
      onTick: (tick) => log('⏰ Tick #$tick'),
    );

    // Iniciar atualizações a cada 5 segundos
    _periodicService.startPeriodicUpdates(isOnline: true);
  }

  void changeToFastUpdates() {
    // Alterar para atualizações a cada 3 segundos
    _periodicService.changeInterval(const Duration(seconds: 3));
  }

  void changeToSlowUpdates() {
    // Alterar para atualizações a cada 10 segundos
    _periodicService.changeInterval(const Duration(seconds: 10));
  }

  void handleOnlineStatusChange(bool isOnline) {
    _periodicService.updateOnlineStatus(isOnline);
  }

  void forceRefresh() {
    _periodicService.forceUpdate();
  }

  void stopUpdates() {
    _periodicService.stopPeriodicUpdates();
  }

  void getUpdateStats() {
    final stats = _periodicService.getStats();
    log('📊 Stats: $stats');
  }

  void dispose() {
    _periodicService.dispose();
  }
}
