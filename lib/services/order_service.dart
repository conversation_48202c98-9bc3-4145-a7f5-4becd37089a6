import 'dart:async';
import 'dart:developer';
import 'dart:ui';

import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:emartdriver/constants.dart';
import 'package:emartdriver/model/OrderModel.dart';
import 'package:emartdriver/services/notification_throttle_service.dart';
import 'package:emartdriver/services/order_state_manager.dart';
import 'package:emartdriver/ui/home/<USER>';
import 'package:emartdriver/ui/home/<USER>/calular_distance.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';

import '../vendor_status_enum.dart';

/// Resultado da operação de aceitar pedido
class OrderAcceptResult {
  final bool success;
  final String message;

  OrderAcceptResult({required this.success, required this.message});
}

class OrderService {
  static final OrderService _instance = OrderService._internal();
  factory OrderService() => _instance;
  OrderService._internal();

  StreamSubscription<QuerySnapshot>? _ordersSubscription;
  StreamSubscription<DocumentSnapshot>? _acceptedOrderSubscription;
  Timer? _periodicUpdateTimer;

  OrderModel? _selectedOrder;
  RouteInfo? _selectedRouteInfo;
  bool _pedidoAceito = false;

  // Configurações do timer
  static const Duration _updateInterval = Duration(seconds: 5);
  LatLng? _lastKnownPosition;
  bool _isOnline = false;

  final String currentUserId = FirebaseAuth.instance.currentUser?.uid ?? '';
  final OrderStateManager _stateManager = OrderStateManager();

  // Callbacks para notificar mudanças
  Function(OrderModel?)? onOrderSelected;
  Function(RouteInfo?)? onRouteInfoChanged;
  Function(bool)? onOrderAcceptedChanged;
  Function(List<OrderModel>, String)? onMultipleOrdersFound;
  VoidCallback? onOrdersCleared;
  VoidCallback? onClearMarkersBeforeUpdate;
  Function(String)? onOrderRemoved;

  // Getters
  OrderModel? get selectedOrder => _selectedOrder;
  RouteInfo? get selectedRouteInfo => _selectedRouteInfo;
  bool get pedidoAceito => _pedidoAceito;

  /// Inicia atualização periódica de pedidos a cada 5 segundos
  void startPeriodicOrderUpdates(LatLng currentPosition, bool isOnline) {
    // Salvar estado atual
    _lastKnownPosition = currentPosition;
    _isOnline = isOnline;

    // Parar timer anterior se existir
    stopPeriodicOrderUpdates();

    // Buscar pedidos imediatamente
    fetchOrders(currentPosition, isOnline);

    // Iniciar timer periódico
    _periodicUpdateTimer = Timer.periodic(_updateInterval, (timer) {
      if (_lastKnownPosition != null && _isOnline) {
        fetchOrders(_lastKnownPosition!, _isOnline);
      }
    });

    log("✅ [PEDIDOS] Atualização automática iniciada (${_updateInterval.inSeconds}s)");
  }

  /// Para a atualização periódica de pedidos
  void stopPeriodicOrderUpdates() {
    if (_periodicUpdateTimer != null) {
      _periodicUpdateTimer!.cancel();
      _periodicUpdateTimer = null;
    }
  }

  /// Atualiza a posição para o timer periódico
  void updatePosition(LatLng newPosition) {
    _lastKnownPosition = newPosition;
  }

  /// Atualiza o status online para o timer periódico
  void updateOnlineStatus(bool isOnline) {
    _isOnline = isOnline;

    if (!isOnline) {
      stopPeriodicOrderUpdates();
    } else if (_lastKnownPosition != null) {
      startPeriodicOrderUpdates(_lastKnownPosition!, isOnline);
    }
  }

  /// Busca pedidos disponíveis baseado na posição atual
  Future<void> fetchOrders(LatLng currentPosition, bool isOnline) async {
    if (!isOnline) {
      onOrdersCleared?.call();
      return;
    }

    try {
      // Verifica pedidos de devolução primeiro
      final returnOrderSnapshot = await FirebaseFirestore.instance
          .collection(ORDERS)
          .where('entregador_id', isEqualTo: currentUserId)
          .where('status', isEqualTo: OrderStatus.delivered.description)
          .where('has_return', isEqualTo: true)
          .limit(1)
          .get();

      if (returnOrderSnapshot.docs.isNotEmpty) {
        final returnOrder =
            OrderModel.fromJson(returnOrderSnapshot.docs.first.data());
        log("Pedido de devolução encontrado: ID=${returnOrder.id}");

        _selectedOrder = returnOrder;
        _pedidoAceito = true;

        onOrderSelected?.call(_selectedOrder);
        onOrderAcceptedChanged?.call(_pedidoAceito);
        return;
      }

      // Verifica pedidos já aceitos
      final existingOrderSnapshot = await FirebaseFirestore.instance
          .collection(ORDERS)
          .where('entregador_id', isEqualTo: currentUserId)
          .where('status', whereIn: [
            OrderStatus.driverAccepted.description,
            OrderStatus.driverOnTheWay.description
          ])
          .limit(1)
          .get();

      if (existingOrderSnapshot.docs.isNotEmpty) {
        final existingOrder =
            OrderModel.fromJson(existingOrderSnapshot.docs.first.data());

        _selectedOrder = existingOrder;
        _pedidoAceito = true;

        onOrderSelected?.call(_selectedOrder);
        onOrderAcceptedChanged?.call(_pedidoAceito);
        return;
      }

      // Busca novos pedidos disponíveis
      await _fetchAvailableOrders(currentPosition);
    } catch (e) {
      log("Erro ao buscar pedidos: $e");
    }
  }

  /// Busca pedidos disponíveis para aceitar
  Future<void> _fetchAvailableOrders(LatLng currentPosition) async {
    // Limpar marcadores antigos antes de buscar novos pedidos
    onClearMarkersBeforeUpdate?.call();

    final snapshot = await FirebaseFirestore.instance
        .collection(ORDERS)
        .where('status', isEqualTo: OrderStatus.driverSearching.description)
        .get();

    Map<String, List<OrderModel>> pedidosPorLoja = {};
    List<OrderModel> allValidOrders = [];

    for (var doc in snapshot.docs) {
      final data = doc.data();
      final order = OrderModel.fromJson(data);

      LatLng? lojaPos =
          _getLatLng(order.vendor.address_store?.location.geoPoint);

      if (lojaPos != null) {
        double distanceInKm = calculateDistance(currentPosition.latitude,
            currentPosition.longitude, lojaPos.latitude, lojaPos.longitude);

        if (distanceInKm <= maxStoreDistanceInKM) {
          String lojaId = order.vendor.id;

          if (!pedidosPorLoja.containsKey(lojaId)) {
            pedidosPorLoja[lojaId] = [];
          }
          pedidosPorLoja[lojaId]!.add(order);
          allValidOrders.add(order);
        }
      }
    }

    // Atualizar estado e obter pedidos que devem ser removidos
    final ordersToRemove = _stateManager.updateOrders(allValidOrders);

    // Notificar sobre pedidos que devem ser removidos
    if (ordersToRemove.isNotEmpty) {
      for (final orderId in ordersToRemove) {
        onOrderRemoved?.call(orderId);
      }
    }

    // Notifica sobre pedidos encontrados
    if (pedidosPorLoja.isEmpty) {
      onOrdersCleared?.call();
    } else {
      if (pedidosPorLoja.isNotEmpty) {
        log("📦 [PEDIDOS] ${pedidosPorLoja.length} lojas com pedidos disponíveis");
      }
      pedidosPorLoja.forEach((lojaId, pedidos) {
        if (pedidos.isNotEmpty) {
          onMultipleOrdersFound?.call(pedidos, lojaId);
        }
      });
    }
  }

  /// Seleciona um pedido sem calcular rota (para uso em notificações)
  void selectOrder(OrderModel order) {
    _selectedOrder = order;
    _pedidoAceito = false;
    onOrderSelected?.call(_selectedOrder);
    onOrderAcceptedChanged?.call(_pedidoAceito);
  }

  /// Seleciona um pedido e calcula informações da rota
  Future<void> selectOrderAndCalculateRoute(
      OrderModel order, LatLng currentPosition) async {
    final loja = _getLatLng(order.vendor.address_store?.location.geoPoint);
    final cliente = _getLatLng(order.author.shippingAddress
        ?.firstWhere((a) => a.isDefault == true)
        .location
        ?.geoPoint);

    if (loja == null || cliente == null) return;

    double storeDistanceInKm = calculateDistance(currentPosition.latitude,
        currentPosition.longitude, loja.latitude, loja.longitude);

    log("Selected order from store at distance: ${storeDistanceInKm.toStringAsFixed(1)}");

    _selectedOrder = order;
    _pedidoAceito = false;

    onOrderSelected?.call(_selectedOrder);
    onOrderAcceptedChanged?.call(_pedidoAceito);

    // Calcula informações da rota
    final routeInfo = await getRouteInfo(loja, cliente);

    _selectedRouteInfo = RouteInfo(
      route: [],
      distance: routeInfo.distance,
      duration: routeInfo.duration,
    );

    onRouteInfoChanged?.call(_selectedRouteInfo);
  }

  /// Aceita um pedido com validação
  Future<OrderAcceptResult> acceptOrderWithValidation() async {
    if (_selectedOrder == null) {
      return OrderAcceptResult(
          success: false, message: "Nenhum pedido selecionado");
    }

    try {
      final docRef =
          FirebaseFirestore.instance.collection(ORDERS).doc(_selectedOrder!.id);

      // Validar se o pedido já tem outro entregador antes de aceitar
      final currentDoc = await docRef.get();
      if (!currentDoc.exists) {
        log("Erro: Pedido não encontrado");
        return OrderAcceptResult(
            success: false, message: "Pedido não encontrado");
      }

      final currentData = currentDoc.data()!;
      final currentEntregadorId = currentData['entregador_id'] as String?;
      log("ID do entregador atual: $currentEntregadorId");
      // Verificar se já tem entregador diferente do atual
      if (currentEntregadorId != null &&
          currentEntregadorId.isNotEmpty &&
          currentEntregadorId != currentUserId) {
        log("Erro: Pedido já foi aceito por outro entregador (ID: $currentEntregadorId)");
        return OrderAcceptResult(
            success: false,
            message: "Este pedido já foi aceito por outro entregador");
      }

      await docRef.update({
        "entregador_id": currentUserId,
        "horaAceite": Timestamp.now(),
        "status": OrderStatus.driverAccepted.description,
      });

      final updatedDoc = await docRef.get();
      final updatedOrder = OrderModel.fromJson(updatedDoc.data()!);

      _selectedOrder = updatedOrder;
      _pedidoAceito = true;

      // Remover o pedido do throttle service (não precisa mais ser controlado)
      final throttleService = NotificationThrottleService();
      throttleService.removeOrder(_selectedOrder!.id);

      onOrderSelected?.call(_selectedOrder);
      onOrderAcceptedChanged?.call(_pedidoAceito);

      // Inicia monitoramento do pedido aceito
      listenToAcceptedOrder(_selectedOrder!.id);

      return OrderAcceptResult(
          success: true, message: "Pedido aceito com sucesso");
    } catch (e) {
      log("Erro ao aceitar pedido: $e");
      return OrderAcceptResult(
          success: false, message: "Erro ao aceitar pedido: $e");
    }
  }

  /// Aceita um pedido (método legado - mantido para compatibilidade)
  Future<bool> acceptOrder() async {
    final result = await acceptOrderWithValidation();
    return result.success;
  }

  /// Monitora um pedido aceito
  void listenToAcceptedOrder(String orderId) async {
    _ordersSubscription?.cancel();

    try {
      final docRef = FirebaseFirestore.instance.collection(ORDERS).doc(orderId);
      final docSnapshot = await docRef.get();

      if (docSnapshot.exists) {
        _handleAcceptedOrderUpdate(docSnapshot);

        _acceptedOrderSubscription = docRef.snapshots().listen((docSnapshot) {
          if (docSnapshot.exists) {
            _handleAcceptedOrderUpdate(docSnapshot);
          }
        }, onError: (e) {
          log("Error listening to accepted order: $e");
        });
      }
    } catch (e) {
      log("Error fetching initial accepted order: $e");
    }
  }

  /// Manipula atualizações do pedido aceito
  void _handleAcceptedOrderUpdate(DocumentSnapshot docSnapshot) async {
    try {
      final existingOrder =
          OrderModel.fromJson(docSnapshot.data() as Map<String, dynamic>);

      _selectedOrder = existingOrder;
      _pedidoAceito = true;

      onOrderSelected?.call(_selectedOrder);
      onOrderAcceptedChanged?.call(_pedidoAceito);
    } catch (e) {
      log("Error handling accepted order update: $e");
    }
  }

  /// Limpa o pedido selecionado
  void clearSelectedOrder() {
    // Se havia um pedido selecionado, remover do throttle service
    if (_selectedOrder != null) {
      final throttleService = NotificationThrottleService();
      throttleService.removeOrder(_selectedOrder!.id);
    }

    _selectedOrder = null;
    _selectedRouteInfo = null;
    _pedidoAceito = false;

    onOrderSelected?.call(null);
    onRouteInfoChanged?.call(null);
    onOrderAcceptedChanged?.call(false);
  }

  /// Converte GeoPoint para LatLng
  LatLng? _getLatLng(GeoPoint? location) {
    if (location == null) return null;
    return LatLng(location.latitude, location.longitude);
  }

  /// Limpa os recursos
  void dispose() {
    _ordersSubscription?.cancel();
    _acceptedOrderSubscription?.cancel();
    stopPeriodicOrderUpdates();

    onOrderSelected = null;
    onRouteInfoChanged = null;
    onOrderAcceptedChanged = null;
    onMultipleOrdersFound = null;
    onOrdersCleared = null;
    onClearMarkersBeforeUpdate = null;
    onOrderRemoved = null;
  }
}
