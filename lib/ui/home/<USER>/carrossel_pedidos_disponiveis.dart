import 'package:emartdriver/model/OrderModel.dart';
import 'package:emartdriver/ui/home/<USER>/calular_distance.dart';
import 'package:flutter/material.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';

import 'cartao_pedido_carrossel.dart';

/// Widget que exibe um carrossel horizontal com os pedidos disponíveis
class CarrosselPedidosDisponiveis extends StatefulWidget {
  final List<OrderModel> pedidos;
  final Function(OrderModel) aoSelecionarPedido;
  final LatLng? posicaoAtual;

  const CarrosselPedidosDisponiveis({
    super.key,
    required this.pedidos,
    required this.aoSelecionarPedido,
    this.posicaoAtual,
  });

  @override
  State<CarrosselPedidosDisponiveis> createState() =>
      _CarrosselPedidosDisponiveisState();
}

class _CarrosselPedidosDisponiveisState
    extends State<CarrosselPedidosDisponiveis> {
  bool _processandoSelecao = false;
  String? _pedidoSendoProcessado;

  @override
  Widget build(BuildContext context) {
    if (widget.pedidos.isEmpty) {
      return const SizedBox.shrink();
    }

    return Container(
      height: 180,
      margin: const EdgeInsets.symmetric(horizontal: 8),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.2),
            blurRadius: 8,
            offset: const Offset(0, -2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _construirCabecalho(),
          const SizedBox(height: 8),
          Expanded(
            child: _construirListaPedidos(),
          ),
          const SizedBox(height: 8),
        ],
      ),
    );
  }

  Widget _construirCabecalho() {
    return Padding(
      padding: const EdgeInsets.fromLTRB(16, 12, 16, 0),
      child: Row(
        children: [
          Container(
            padding: const EdgeInsets.all(6),
            decoration: BoxDecoration(
              color: const Color(0xff425799).withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(8),
            ),
            child: const Icon(
              Icons.delivery_dining,
              color: Color(0xff425799),
              size: 20,
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const Text(
                  'Pedidos Disponíveis',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                    color: Color(0xff425799),
                  ),
                ),
                Text(
                  '${widget.pedidos.length} ${widget.pedidos.length == 1 ? 'pedido disponível' : 'pedidos disponíveis'}',
                  style: TextStyle(
                    fontSize: 12,
                    color: Colors.grey[600],
                  ),
                ),
              ],
            ),
          ),
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
            decoration: BoxDecoration(
              color: Colors.green[50],
              borderRadius: BorderRadius.circular(12),
              border: Border.all(color: Colors.green, width: 1),
            ),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                Icon(
                  Icons.trending_up,
                  size: 14,
                  color: Colors.green[700],
                ),
                const SizedBox(width: 4),
                Text(
                  'Novos',
                  style: TextStyle(
                    fontSize: 11,
                    fontWeight: FontWeight.w600,
                    color: Colors.green[700],
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _construirListaPedidos() {
    return ListView.builder(
      scrollDirection: Axis.horizontal,
      padding: const EdgeInsets.symmetric(horizontal: 16),
      itemCount: widget.pedidos.length,
      itemBuilder: (context, index) {
        final pedido = widget.pedidos[index];
        final distanciaKm = _calcularDistancia(pedido);

        final bool estaSendoProcessado = _pedidoSendoProcessado == pedido.id;

        return CartaoPedidoCarrossel(
          pedido: pedido,
          aoSelecionarPedido: () => _selecionarPedido(pedido),
          distanciaKm: distanciaKm,
          desabilitado: _processandoSelecao,
          processando: estaSendoProcessado,
        );
      },
    );
  }

  /// Método para controlar a seleção de pedidos e evitar cliques duplos
  void _selecionarPedido(OrderModel pedido) {
    // Verificar se já está processando uma seleção
    if (_processandoSelecao) {
      return;
    }

    // Verificar se este pedido já está sendo processado
    if (_pedidoSendoProcessado == pedido.id) {
      return;
    }

    // Marcar como processando
    setState(() {
      _processandoSelecao = true;
      _pedidoSendoProcessado = pedido.id;
    });

    // Chamar a função de seleção
    widget.aoSelecionarPedido(pedido);

    // Resetar o estado após um delay para permitir nova seleção
    Future.delayed(const Duration(seconds: 2), () {
      if (mounted) {
        setState(() {
          _processandoSelecao = false;
          _pedidoSendoProcessado = null;
        });
      }
    });
  }

  double? _calcularDistancia(OrderModel pedido) {
    if (widget.posicaoAtual == null) return null;

    final lojaLocation = pedido.vendor.address_store?.location.geoPoint;
    if (lojaLocation == null) return null;

    return calculateDistance(
      widget.posicaoAtual!.latitude,
      widget.posicaoAtual!.longitude,
      lojaLocation.latitude,
      lojaLocation.longitude,
    );
  }
}
