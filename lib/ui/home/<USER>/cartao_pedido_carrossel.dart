import 'package:emartdriver/model/OrderModel.dart';
import 'package:flutter/material.dart';

import 'extensions/order_extensions.dart';

/// Widget compacto para exibir pedidos no carrossel horizontal
class CartaoPedidoCarrossel extends StatelessWidget {
  final OrderModel pedido;
  final VoidCallback aoSelecionarPedido;
  final double? distanciaKm;
  final bool desabilitado;
  final bool processando;

  const CartaoPedidoCarrossel({
    super.key,
    required this.pedido,
    required this.aoSelecionarPedido,
    this.distanciaKm,
    this.desabilitado = false,
    this.processando = false,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      width: 280,
      margin: const EdgeInsets.only(right: 12),
      child: Card(
        elevation: 3,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(12),
          side: const BorderSide(color: Color(0xff425799), width: 1),
        ),
        child: InkWell(
          onTap: desabilitado ? null : aoSelecionarPedido,
          borderRadius: BorderRadius.circular(12),
          child: Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: desabilitado
                  ? Colors.grey.withValues(alpha: 0.3)
                  : processando
                      ? const Color(0xff425799).withValues(alpha: 0.1)
                      : Colors.transparent,
              borderRadius: BorderRadius.circular(12),
            ),
            child: Stack(
              children: [
                Opacity(
                  opacity: desabilitado ? 0.5 : 1.0,
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      _construirCabecalho(),
                      const SizedBox(height: 8),
                      _construirInformacaoLoja(),
                      const SizedBox(height: 6),
                      _construirInformacaoDistancia(),
                    ],
                  ),
                ),
                if (processando)
                  Positioned.fill(
                    child: Container(
                      decoration: BoxDecoration(
                        color: const Color(0xff425799).withValues(alpha: 0.1),
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: const Center(
                        child: SizedBox(
                          width: 20,
                          height: 20,
                          child: CircularProgressIndicator(
                            strokeWidth: 2,
                            valueColor: AlwaysStoppedAnimation<Color>(
                                Color(0xff425799)),
                          ),
                        ),
                      ),
                    ),
                  ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _construirCabecalho() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Expanded(
          child: Text(
            'Pedido #${pedido.id.obterIdReduzido()}',
            style: const TextStyle(
              fontWeight: FontWeight.bold,
              fontSize: 14,
              color: Color(0xff425799),
            ),
            overflow: TextOverflow.ellipsis,
          ),
        ),
        Container(
          padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
          decoration: BoxDecoration(
            color: Colors.green[50],
            borderRadius: BorderRadius.circular(6),
            border: Border.all(color: Colors.green, width: 1),
          ),
          child: Text(
            'R\$ ${pedido.payValueDriver.formatarValorMonetario()}',
            style: const TextStyle(
              fontWeight: FontWeight.bold,
              fontSize: 12,
              color: Colors.green,
            ),
          ),
        ),
      ],
    );
  }

  Widget _construirInformacaoLoja() {
    return Row(
      children: [
        const Icon(
          Icons.store,
          size: 16,
          color: Color(0xff425799),
        ),
        const SizedBox(width: 4),
        Expanded(
          child: Text(
            pedido.vendor.title,
            style: const TextStyle(
              fontSize: 12,
              fontWeight: FontWeight.w600,
              color: Color(0xff425799),
            ),
            overflow: TextOverflow.ellipsis,
          ),
        ),
      ],
    );
  }

  Widget _construirInformacaoCliente() {
    return Row(
      children: [
        const Icon(
          Icons.person,
          size: 16,
          color: Colors.grey,
        ),
        const SizedBox(width: 4),
        Expanded(
          child: Text(
            '${pedido.author.firstName} ${pedido.author.lastName}',
            style: const TextStyle(
              fontSize: 11,
              color: Colors.grey,
            ),
            overflow: TextOverflow.ellipsis,
          ),
        ),
      ],
    );
  }

  Widget _construirInformacaoDistancia() {
    if (distanciaKm == null) {
      return const SizedBox.shrink();
    }

    return Row(
      children: [
        const Icon(
          Icons.location_on,
          size: 16,
          color: Colors.orange,
        ),
        const SizedBox(width: 4),
        Text(
          "Distancia: ${distanciaKm!.toStringAsFixed(1)} km",
          style: const TextStyle(
            fontSize: 11,
            color: Colors.orange,
            fontWeight: FontWeight.w500,
          ),
        ),
      ],
    );
  }
}
