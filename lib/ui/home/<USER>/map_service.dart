import 'dart:developer';
import 'dart:ui' as ui;

import 'package:flutter/services.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';

class MapService {
  BitmapDescriptor? _deliveryPersonIcon;

  BitmapDescriptor? get deliveryPersonIcon => _deliveryPersonIcon;

  Future<void> loadCustomMarkerIcon() async {
    try {
      final Uint8List markerIcon =
          await getBytesFromAsset('assets/images/motoentregador.png', 40);
      _deliveryPersonIcon = BitmapDescriptor.bytes(markerIcon);

      _deliveryPersonIcon ??=
          BitmapDescriptor.defaultMarkerWithHue(BitmapDescriptor.hueAzure);
    } catch (e) {
      log("Error in loadCustomMarkerIcon: $e");

      _deliveryPersonIcon =
          BitmapDescriptor.defaultMarkerWithHue(BitmapDescriptor.hueAzure);
    }
  }

  Future<Uint8List> getBytesFromAsset(String path, int width) async {
    ByteData data = await rootBundle.load(path);
    ui.Codec codec = await ui.instantiateImageCodec(data.buffer.asUint8List(),
        targetWidth: width);
    ui.FrameInfo fi = await codec.getNextFrame();
    return (await fi.image.toByteData(format: ui.ImageByteFormat.png))!
        .buffer
        .asUint8List();
  }

  void clearMarkersExceptCurrentPosition(Map<String, Marker> markers) {
    markers.removeWhere((key, value) => key != 'current_position');
  }

  void forceMapReload(GoogleMapController mapController) {
    // Implementação para forçar reload do mapa se necessário
  }

  void updateCurrentPositionMarker(Map<String, Marker> markers,
      LatLng currentPosition, BitmapDescriptor? icon) {
    markers['current_position'] = Marker(
      markerId: const MarkerId('current_position'),
      position: currentPosition,
      icon: icon ?? BitmapDescriptor.defaultMarker,
      infoWindow: const InfoWindow(title: 'Sua localização'),
    );
  }
}
