import 'package:emartdriver/model/OrderModel.dart';
import 'package:emartdriver/ui/home/<USER>';
import 'package:emartdriver/vendor_status_enum.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:url_launcher/url_launcher.dart';

class PersistentOrderBar extends StatelessWidget {
  final bool pedidoAceito;
  final OrderModel? selectedOrder;
  final double? distanceKm;

  const PersistentOrderBar({
    Key? key,
    required this.pedidoAceito,
    required this.selectedOrder,
    this.distanceKm,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    if (!pedidoAceito || selectedOrder == null) {
      return const SizedBox.shrink();
    }

    // Só mostrar para status específicos: Entrega aceita e Entrega a caminho
    if (selectedOrder!.status != OrderStatus.driverAccepted &&
        selectedOrder!.status != OrderStatus.driverOnTheWay) {
      return const SizedBox.shrink();
    }

    final double distance = distanceKm ?? 0.0;

    String statusText;
    String destinationText;
    String secondaryText;
    Color statusColor;
    bool showingClientInfo = false;

    if (selectedOrder!.status == OrderStatus.driverAccepted) {
      // Entrega aceita - Mostrar endereço da loja
      statusText = "Entrega aceita";
      destinationText = "Loja: ${selectedOrder!.vendor.title}";

      // Mostrar endereço da loja
      String lojaEndereco = "";
      if (selectedOrder!.vendor.address_store != null) {
        final enderecoLoja = selectedOrder!.vendor.address_store!;
        final List<String> partesEndereco = [];

        if (enderecoLoja.bairro != null && enderecoLoja.bairro!.isNotEmpty) {
          partesEndereco.add(enderecoLoja.bairro!);
        }
        if (enderecoLoja.logradouro != null &&
            enderecoLoja.logradouro!.isNotEmpty) {
          partesEndereco.add(enderecoLoja.logradouro!);
        }
        if (enderecoLoja.numero != null && enderecoLoja.numero!.isNotEmpty) {
          partesEndereco.add("nº ${enderecoLoja.numero!}");
        }

        if (partesEndereco.isNotEmpty) {
          lojaEndereco = partesEndereco.join(', ');
        }
      }

      secondaryText =
          lojaEndereco.isNotEmpty ? lojaEndereco : "Endereço da loja";
      statusColor = Colors.blue;
      showingClientInfo = false;
    } else {
      // Entrega a caminho - Mostrar endereço do cliente
      statusText = "Entrega a caminho";
      final clientName =
          "${selectedOrder!.author.firstName} ${selectedOrder!.author.lastName}"
              .trim();
      final clientAddress = selectedOrder!.author.shippingAddress?.firstWhere(
          (a) => a.isDefault == true,
          orElse: () => selectedOrder!.author.shippingAddress!.first);

      destinationText =
          "Cliente: ${clientName.isNotEmpty ? clientName : 'Cliente'}";

      // Mostrar endereço do cliente
      String clienteEndereco = "";
      if (clientAddress != null) {
        final List<String> partesEndereco = [];

        if (clientAddress.bairro != null && clientAddress.bairro!.isNotEmpty) {
          partesEndereco.add(clientAddress.bairro!);
        }
        if (clientAddress.logradouro != null &&
            clientAddress.logradouro!.isNotEmpty) {
          partesEndereco.add(clientAddress.logradouro!);
        }
        if (clientAddress.numero != null && clientAddress.numero!.isNotEmpty) {
          partesEndereco.add("nº ${clientAddress.numero!}");
        }

        if (partesEndereco.isNotEmpty) {
          clienteEndereco = partesEndereco.join(', ');
        }
      }

      secondaryText =
          clienteEndereco.isNotEmpty ? clienteEndereco : "Endereço do cliente";
      statusColor = Colors.orange;
      showingClientInfo = true;
    }

    return Container(
      margin: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          borderRadius: BorderRadius.circular(16),
          onTap: () {
            showStoreInfoDialog(
              context,
              selectedOrder!,
            );
          },
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    const Text(
                      " Pedido",
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                        color: Colors.black87,
                      ),
                    ),
                    Text(
                      "#${selectedOrder!.id.substring(0, 6)}",
                      style: const TextStyle(
                        fontSize: 14,
                        color: Colors.grey,
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 8),
                Row(
                  children: [
                    Container(
                      padding: const EdgeInsets.symmetric(
                          horizontal: 8, vertical: 4),
                      decoration: BoxDecoration(
                        color: statusColor,
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: Text(
                        statusText,
                        style: const TextStyle(
                          color: Colors.white,
                          fontSize: 12,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                    const Spacer(),
                  ],
                ),
                const SizedBox(height: 8),
                Row(
                  children: [
                    const Icon(
                      Icons.location_on,
                      size: 16,
                      color: Color(0xff425799),
                    ),
                    const SizedBox(width: 4),
                    Expanded(
                      child: Text(
                        destinationText,
                        style: const TextStyle(
                          fontSize: 14,
                          color: Color(0xff425799),
                        ),
                        overflow: TextOverflow.ellipsis,
                      ),
                    ),
                    if (distance > 0) ...[
                      const SizedBox(width: 8),
                      Text(
                        distance.toString(),
                        style: const TextStyle(
                          fontSize: 12,
                          color: Colors.grey,
                        ),
                      ),
                    ],
                  ],
                ),
                const SizedBox(height: 8),
                Row(
                  children: [
                    Icon(
                      showingClientInfo ? Icons.home : Icons.store,
                      size: 16,
                      color: const Color(0xff425799),
                    ),
                    const SizedBox(width: 4),
                    Expanded(
                      child: Text(
                        secondaryText,
                        style: const TextStyle(
                          fontSize: 14,
                          color: Color(0xff425799),
                        ),
                        overflow: TextOverflow.visible,
                      ),
                    ),
                  ],
                ),
                // Mostrar número do cliente quando estiver em entrega
                if (showingClientInfo &&
                    selectedOrder!.author.phoneNumber.isNotEmpty) ...[
                  const SizedBox(height: 8),
                  InkWell(
                    onTap: () async {
                      final scaffoldMessenger = ScaffoldMessenger.of(context);
                      final phoneNumber = selectedOrder!.author.phoneNumber;

                      // Limpar o número (remover caracteres especiais)
                      final cleanNumber =
                          phoneNumber.replaceAll(RegExp(r'[^\d]'), '');

                      // URL do WhatsApp
                      final whatsappUrl = 'https://wa.me/55$cleanNumber';

                      try {
                        final uri = Uri.parse(whatsappUrl);
                        if (await canLaunchUrl(uri)) {
                          await launchUrl(uri,
                              mode: LaunchMode.externalApplication);
                        } else {
                          // Fallback: copiar número se não conseguir abrir WhatsApp
                          await Clipboard.setData(
                              ClipboardData(text: phoneNumber));
                          scaffoldMessenger.showSnackBar(
                            SnackBar(
                              content: Text(
                                  'WhatsApp não encontrado. Número copiado: $phoneNumber'),
                              duration: const Duration(seconds: 3),
                              backgroundColor: Colors.orange,
                            ),
                          );
                        }
                      } catch (e) {
                        // Fallback: copiar número em caso de erro
                        await Clipboard.setData(
                            ClipboardData(text: phoneNumber));
                        scaffoldMessenger.showSnackBar(
                          SnackBar(
                            content: Text(
                                'Erro ao abrir WhatsApp. Número copiado: $phoneNumber'),
                            duration: const Duration(seconds: 3),
                            backgroundColor: Colors.red,
                          ),
                        );
                      }
                    },
                    borderRadius: BorderRadius.circular(8),
                    child: Padding(
                      padding: const EdgeInsets.symmetric(
                        horizontal: 8,
                        vertical: 4,
                      ),
                      child: Row(
                        children: [
                          const Icon(
                            Icons.phone,
                            size: 16,
                            color: Color(0xff425799),
                          ),
                          const SizedBox(width: 4),
                          Expanded(
                            child: Text(
                              "Tel: ${selectedOrder!.author.phoneNumber}",
                              style: const TextStyle(
                                fontSize: 14,
                                color: Color(0xff425799),
                                fontWeight: FontWeight.w500,
                              ),
                            ),
                          ),
                          const SizedBox(width: 4),
                          const Icon(
                            Icons.chat,
                            size: 14,
                            color: Color(0xff25D366), // Cor verde do WhatsApp
                          ),
                        ],
                      ),
                    ),
                  ),
                ],
                const SizedBox(height: 8),
                Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Icon(
                      Icons.touch_app,
                      size: 16,
                      color: Colors.grey[400],
                    ),
                    const SizedBox(width: 4),
                    Text(
                      "Toque para ver detalhes",
                      style: TextStyle(
                        fontSize: 12,
                        color: Colors.grey[400],
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
