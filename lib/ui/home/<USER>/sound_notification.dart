import 'package:audioplayers/audioplayers.dart';
import 'package:flutter/services.dart';

playSound() async {
  // Toca o som 3 vezes com pausa de 1 segundo entre elas
  for (int i = 0; i < 3; i++) {
    final audioPlayer = AudioPlayer();
    final path = await rootBundle.load("assets/audio/notification.wav");
    audioPlayer.setSourceBytes(path.buffer.asUint8List());
    audioPlayer.setReleaseMode(ReleaseMode.release);

    // Aguarda a reprodução terminar antes de continuar
    await audioPlayer.play(
      BytesSource(path.buffer.asUint8List()),
      volume: 100,
      ctx: AudioContext(
        android: const AudioContextAndroid(
            contentType: AndroidContentType.music,
            isSpeakerphoneOn: true,
            stayAwake: true,
            usageType: AndroidUsageType.alarm,
            audioFocus: AndroidAudioFocus.gainTransient),
        iOS: AudioContextIOS(category: AVAudioSessionCategory.playback),
      ),
    );

    // Aguarda a reprodução terminar
    await audioPlayer.onPlayerComplete.first;

    // Limpa o player
    await audioPlayer.stop();
    await audioPlayer.dispose();

    // Pausa de 1 segundo entre as reproduções (exceto na última)
    if (i < 2) {
      await Future.delayed(const Duration(seconds: 1));
    }
  }
}
