import 'package:emartdriver/constants.dart';
import 'package:emartdriver/services/verificacao_atualizacao_service.dart';
import 'package:flutter/material.dart';

/// Tela para verificação manual de atualizações
/// Pode ser usada em configurações ou como opção no menu
class VerificacaoAtualizacaoScreen extends StatefulWidget {
  const VerificacaoAtualizacaoScreen({super.key});

  @override
  State<VerificacaoAtualizacaoScreen> createState() =>
      _VerificacaoAtualizacaoScreenState();
}

class _VerificacaoAtualizacaoScreenState
    extends State<VerificacaoAtualizacaoScreen> {
  final VerificacaoAtualizacaoService _service =
      VerificacaoAtualizacaoService();
  bool _verificando = false;
  Map<String, dynamic>? _informacoesAtualizacao;
  String? _mensagemStatus;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Verificar Atualizações'),
        backgroundColor: Color(COLOR_PRIMARY),
        foregroundColor: Colors.white,
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            _buildHeaderCard(),
            const SizedBox(height: 20),
            _buildVerificarButton(),
            const SizedBox(height: 20),
            if (_informacoesAtualizacao != null) _buildInformacoesCard(),
            if (_mensagemStatus != null) _buildStatusCard(),
          ],
        ),
      ),
    );
  }

  Widget _buildHeaderCard() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          children: [
            Icon(
              Icons.system_update,
              size: 64,
              color: Color(COLOR_PRIMARY),
            ),
            const SizedBox(height: 16),
            const Text(
              'Verificação de Atualizações',
              style: TextStyle(
                fontSize: 20,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 8),
            const Text(
              'Verifique se há uma nova versão do aplicativo disponível na loja.',
              textAlign: TextAlign.center,
              style: TextStyle(
                fontSize: 14,
                color: Colors.grey,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildVerificarButton() {
    return ElevatedButton(
      onPressed: _verificando ? null : _verificarAtualizacoes,
      style: ElevatedButton.styleFrom(
        backgroundColor: Color(COLOR_PRIMARY),
        foregroundColor: Colors.white,
        padding: const EdgeInsets.symmetric(vertical: 16),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(8),
        ),
      ),
      child: _verificando
          ? const Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                SizedBox(
                  width: 20,
                  height: 20,
                  child: CircularProgressIndicator(
                    strokeWidth: 2,
                    valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                  ),
                ),
                SizedBox(width: 12),
                Text('Verificando...'),
              ],
            )
          : const Text(
              'Verificar Atualizações',
              style: TextStyle(fontSize: 16),
            ),
    );
  }

  Widget _buildInformacoesCard() {
    final info = _informacoesAtualizacao!;
    final isObrigatoria = info['isObrigatoria'] ?? false;

    return Card(
      color: isObrigatoria ? Colors.red.shade50 : Colors.blue.shade50,
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  isObrigatoria ? Icons.warning : Icons.info,
                  color: isObrigatoria ? Colors.red : Colors.blue,
                ),
                const SizedBox(width: 8),
                Text(
                  isObrigatoria
                      ? 'Atualização Obrigatória'
                      : 'Atualização Disponível',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: isObrigatoria ? Colors.red : Colors.blue,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            _buildInfoRow(
                'Versão Atual:', info['versaoAtual'] ?? 'Desconhecida'),
            const SizedBox(height: 8),
            _buildInfoRow(
                'Nova Versão:', info['versaoDisponivel'] ?? 'Desconhecida'),
            const SizedBox(height: 16),
            Text(
              info['descricao'] ?? 'Nova versão disponível.',
              style: const TextStyle(fontSize: 14),
            ),
            const SizedBox(height: 16),
            SizedBox(
              width: double.infinity,
              child: ElevatedButton(
                onPressed: _atualizarApp,
                style: ElevatedButton.styleFrom(
                  backgroundColor: isObrigatoria ? Colors.red : Colors.blue,
                  foregroundColor: Colors.white,
                ),
                child: const Text('Atualizar Agora'),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildStatusCard() {
    return Card(
      color: Colors.green.shade50,
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Row(
          children: [
            const Icon(
              Icons.check_circle,
              color: Colors.green,
            ),
            const SizedBox(width: 12),
            Expanded(
              child: Text(
                _mensagemStatus!,
                style: const TextStyle(
                  fontSize: 16,
                  color: Colors.green,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildInfoRow(String label, String value) {
    return Row(
      children: [
        Text(
          label,
          style: const TextStyle(
            fontWeight: FontWeight.w500,
            color: Colors.grey,
          ),
        ),
        const SizedBox(width: 8),
        Text(
          value,
          style: const TextStyle(
            fontWeight: FontWeight.bold,
          ),
        ),
      ],
    );
  }

  Future<void> _verificarAtualizacoes() async {
    setState(() {
      _verificando = true;
      _informacoesAtualizacao = null;
      _mensagemStatus = null;
    });

    try {
      final informacoes = await _service.obterInformacoesAtualizacao();

      setState(() {
        _verificando = false;
        if (informacoes != null) {
          _informacoesAtualizacao = informacoes;
        } else {
          _mensagemStatus =
              'Você está usando a versão mais recente do aplicativo!';
        }
      });
    } catch (e) {
      setState(() {
        _verificando = false;
        _mensagemStatus =
            'Erro ao verificar atualizações. Tente novamente mais tarde.';
      });
    }
  }

  void _atualizarApp() async {
    _service.exibirDialogAtualizacao(
      informacoes: _informacoesAtualizacao!,
      onAtualizar: () {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Redirecionando para a loja...'),
            backgroundColor: Colors.green,
          ),
        );
      },
      onCancelar: () {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Atualização cancelada'),
            backgroundColor: Colors.orange,
          ),
        );
      },
    );
  }
}
