class WalletTransactionModel {
  final int id;
  final int tipo; // 0 = crédito, 1 = débito
  final double valor;
  final String saldo;
  final int status;
  final int idCliente;
  final String? idTransacao;
  final String dataTransacao;
  final String dataRegistro;
  final int perfil;
  final String label;

  WalletTransactionModel({
    required this.id,
    required this.tipo,
    required this.valor,
    required this.saldo,
    required this.status,
    required this.idCliente,
    this.idTransacao,
    required this.dataTransacao,
    required this.dataRegistro,
    required this.perfil,
    required this.label,
  });

  factory WalletTransactionModel.fromJson(Map<String, dynamic> json) {
    return WalletTransactionModel(
      id: int.tryParse(json['id'].toString()) ?? 0,
      tipo: int.tryParse(json['tipo'].toString()) ?? 0,
      valor: double.tryParse(
              json['valor']?.toString().replaceAll(',', '.') ?? '0') ??
          0.0,
      saldo: json['saldo'] ?? '0,00',
      status: int.tryParse(json['status'].toString()) ?? 0,
      idCliente: int.tryParse(json['id_cliente'].toString()) ?? 0,
      idTransacao: json['id_transacao']?.toString(),
      dataTransacao: json['data_transacao'] ?? '',
      dataRegistro: json['data_registro'] ?? '',
      perfil: int.tryParse(json['perfil'].toString()) ?? 0,
      label: json['label'] ?? '',
    );
  }

  bool isCredit(){
    return tipo == 1;
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'tipo': tipo,
      'valor': valor,
      'saldo': saldo,
      'status': status,
      'id_cliente': idCliente,
      'id_transacao': idTransacao,
      'data_transacao': dataTransacao,
      'data_registro': dataRegistro,
      'perfil': perfil,
      'label': label,
    };
  }
}
